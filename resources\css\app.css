@tailwind base;
@tailwind components;
@tailwind utilities;
/* @tailwind variants; */

/* Import CSS untuk button loading */
@import './filament/admin/button-loading.css';

/* PenyairRadio Custom Styling */
.penyair-radio-button label {
    transition: all 0.2s ease-in-out;
    background-color: #f3f4f6; /* Light gray background */
}

.penyair-radio-button input[type="radio"]:checked + label {
    background-color: #6b7280; /* Gray-500 background for selected */
    color: white;
    border-color: #6b7280;
}

.penyair-radio-button label:hover {
    background-color: #e5e7eb; /* Gray-200 on hover */
}

.dark .penyair-radio-button label {
    background-color: #374151; /* Dark mode background */
    border-color: #4b5563;
    color: #e5e7eb;
}

.dark .penyair-radio-button input[type="radio"]:checked + label {
    background-color: #6b7280; /* Gray-500 in dark mode */
    color: white;
    border-color: #9ca3af;
}

.dark .penyair-radio-button label:hover {
    background-color: #4b5563; /* Gray-600 on hover in dark mode */
}

/* Icon styling for PenyairRadio */
.penyair-radio-button .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
}

.penyair-radio-button .icon svg {
    width: 1rem;
    height: 1rem;
}

.penyair-radio-button input[type="radio"]:checked + label .icon svg {
    color: white;
}