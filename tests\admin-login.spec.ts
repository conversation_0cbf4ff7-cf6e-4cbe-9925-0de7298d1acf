import { test, expect } from '@playwright/test';

// Test untuk memastikan proses login admin berhasil
// Komentar dalam Bahasa Indonesia sesuai preferensi pengguna

test('login admin dengan kredensial valid', async ({ page }) => {
  // Buka halaman login admin
  await page.goto('https://hotel.rid/admin');

  // Klik tombol quick login
  await page.getByRole('button', { name: /penyair \(penyair@gmail\.com\)/i }).click();

  // Tunggu navigasi selesai dan verifikasi URL mengarah ke dashboard admin
  await page.waitForLoadState('networkidle');
  await expect(page).toHaveURL(/\/admin(\/dashboard)?/);

  // Cek elemen khas dashboard untuk memastikan login sukses, misalnya logo Filament atau teks "Dashboard"
  // Silakan sesuaikan selector berikut sesuai UI sebenarnya
  const dashboardHeading = page.locator('h1:has-text("Dashboard")');
  await expect(dashboardHeading).toBeVisible();

  // Navigasi ke halaman edit CMS dengan ID 6
  await page.goto('https://hotel.rid/admin/cms/6/edit');
  
  // Tunggu halaman edit CMS selesai dimuat
  await page.waitForLoadState('networkidle');
  
  // Verifikasi bahwa kita berada di halaman edit CMS
  await expect(page).toHaveURL(/\/admin\/cms\/6\/edit/);
  
  // Verifikasi elemen form edit terlihat (misalnya field judul atau tombol simpan)
  const editForm = page.locator('form');
  await expect(editForm).toBeVisible();

  // Lakukan perintah CTRL + U untuk melihat source code halaman
  await page.keyboard.press('Control+U');
  
  // Tunggu tab baru terbuka dengan source code
  await page.waitForTimeout(2000);
  
  // Verifikasi bahwa source code ditampilkan (biasanya di tab baru atau window baru)
  // Pada beberapa browser, CTRL+U membuka tab baru dengan view-source: URL
  const pages = page.context().pages();
  if (pages.length > 1) {
    const sourceTab = pages[pages.length - 1];
    await expect(sourceTab).toHaveURL(/view-source:|data:text\/html/);
  }
}); 