APP_NAME="NEWHOTEL"
APP_ENV=local
APP_KEY=base64:mCSPLEVdzzf0govUVuj+v5lKspwVRSxwLSwih9WMM2M=
APP_DEBUG=true

APP_URL=https://hotel.rid
APP_VERSION=1.0.0

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
SATPAM=true

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hotel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=public
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="uxcg vipr ykqj libu"
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

 

KASIR_PANEL_LOCAL_LOGINS_ENABLED=true
KASIR_PANEL_LOCAL_LOGIN_EMAILS="<EMAIL>,<EMAIL>,<EMAIL>"
VITE_ENABLE_HMR=true

# Konfigurasi Backup Otomatis
AUTO_BACKUP_ENABLED=true
BACKUP_NOTIFICATION_EMAIL=<EMAIL>
DEBUGBAR_ENABLED=false

# MONGODB_URI=mongodb://mongodb:27017
# MONGODB_DATABASE=laravel
# WWWGROUP=1000
# WWWUSER=1000
