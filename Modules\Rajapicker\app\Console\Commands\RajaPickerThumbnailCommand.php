<?php

namespace Modules\Rajapicker\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON><PERSON>\Rajapicker\Services\RajaPickerThumbnailService;
use Mo<PERSON>les\Rajapicker\Services\RajaPickerConfigService;
use Illuminate\Support\Facades\Storage;

class RajaPickerThumbnailCommand extends Command
{
    protected $signature = 'rajapicker:thumbnail 
                            {action : Action yang akan dilakukan (generate, regenerate, cleanup, config, batch, test)}
                            {--file= : Path file untuk action tertentu}
                            {--size=small : Ukuran thumbnail (small, medium, large)}
                            {--collection= : Collection untuk batch processing}
                            {--force : Force regenerate tanpa konfirmasi}
                            {--file_path= : Path file untuk action test}
                            {--size_name=small : Ukuran thumbnail untuk action test}';

    protected $description = 'Manage RajaPicker thumbnails';

    protected RajaPickerThumbnailService $thumbnailService;
    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        parent::__construct();
        $this->thumbnailService = new RajaPickerThumbnailService();
        $this->configService = new RajaPickerConfigService();
    }

    public function handle()
    {
        $action = $this->argument('action');
        $filePath = $this->argument('file_path');
        $sizeName = $this->argument('size_name') ?? 'small';

        // Cek apakah thumbnail diaktifkan
        if (!$this->configService->isThumbnailEnabled()) {
            $this->error('❌ Thumbnail generation tidak diaktifkan di konfigurasi');
            return 1;
        }

        switch ($action) {
            case 'generate':
                $this->generateThumbnail($filePath, $sizeName);
                break;
            case 'regenerate':
                $this->regenerateThumbnail($filePath, $sizeName);
                break;
            case 'cleanup':
                $this->cleanupThumbnails();
                break;
            case 'config':
                $this->showConfig();
                break;
            case 'batch':
                $this->batchGenerate($filePath);
                break;
            case 'test':
                $this->testThumbnail($filePath);
                break;
            default:
                $this->error("Action tidak valid: {$action}");
                $this->showHelp();
                return 1;
        }

        return 0;
    }

    protected function generateThumbnail(string $filePath, string $sizeName): int
    {
        if (!$filePath) {
            $this->error('❌ Option --file diperlukan untuk action generate');
            return 1;
        }

        // Cek apakah file ada
        if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
            $this->error("❌ File tidak ditemukan: {$filePath}");
            return 1;
        }

        $this->info("🔄 Generating thumbnail untuk: {$filePath}");
        $this->info("📏 Ukuran: {$sizeName}");

        $thumbnailPath = $this->thumbnailService->generateThumbnail($filePath, $sizeName);

        if ($thumbnailPath) {
            $thumbnailUrl = $this->configService->generateThumbnailUrl($filePath, $sizeName);
            $this->info("✅ Thumbnail berhasil dibuat:");
            $this->info("   Path: {$thumbnailPath}");
            $this->info("   URL: {$thumbnailUrl}");
            return 0;
        } else {
            $this->error("❌ Gagal membuat thumbnail");
            return 1;
        }
    }

    protected function regenerateThumbnail(string $filePath, string $sizeName): int
    {
        if (!$filePath) {
            $this->error('❌ Option --file diperlukan untuk action regenerate');
            return 1;
        }

        // Cek apakah file ada
        if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
            $this->error("❌ File tidak ditemukan: {$filePath}");
            return 1;
        }

        if (!$this->confirm("Apakah Anda yakin ingin regenerate thumbnail untuk: {$filePath}?")) {
            $this->info("❌ Operasi dibatalkan");
            return 0;
        }

        $this->info("🔄 Regenerating thumbnail untuk: {$filePath}");
        $this->info("📏 Ukuran: {$sizeName}");

        $thumbnailPath = $this->thumbnailService->regenerateThumbnail($filePath, $sizeName);

        if ($thumbnailPath) {
            $thumbnailUrl = $this->configService->generateThumbnailUrl($filePath, $sizeName);
            $this->info("✅ Thumbnail berhasil di-regenerate:");
            $this->info("   Path: {$thumbnailPath}");
            $this->info("   URL: {$thumbnailUrl}");
            return 0;
        } else {
            $this->error("❌ Gagal regenerate thumbnail");
            return 1;
        }
    }

    protected function cleanupThumbnails(): int
    {
        if (!$this->confirm("Apakah Anda yakin ingin menghapus thumbnail yang tidak terpakai?")) {
            $this->info("❌ Operasi dibatalkan");
            return 0;
        }

        $this->info("🧹 Cleaning up orphaned thumbnails...");

        $deletedCount = $this->thumbnailService->cleanupOrphanedThumbnails();

        if ($deletedCount > 0) {
            $this->info("✅ Berhasil menghapus {$deletedCount} thumbnail yang tidak terpakai");
        } else {
            $this->info("ℹ️ Tidak ada thumbnail yang perlu dihapus");
        }

        return 0;
    }

    protected function showConfig(): int
    {
        $this->info("📋 Konfigurasi Thumbnail RajaPicker:");
        $this->newLine();

        $config = [
            'enabled' => $this->configService->isThumbnailEnabled(),
            'directory' => $this->configService->getThumbnailDirectory(),
            'prefix' => $this->configService->getThumbnailPrefix(),
            'webp_conversion' => $this->configService->isThumbnailWebpEnabled(),
        ];

        $this->table(
            ['Property', 'Value'],
            collect($config)->map(function ($value, $key) {
                return [
                    $key,
                    is_bool($value) ? ($value ? 'Yes' : 'No') : $value,
                ];
            })->toArray()
        );

        $this->newLine();
        $this->info("📏 Ukuran Thumbnail:");

        $sizes = $this->configService->getThumbnailSizes();
        $sizeData = collect($sizes)->map(function ($size, $name) {
            return [
                $name,
                $size['width'] . 'x' . $size['height'],
                $size['quality'] . '%',
                $size['suffix'],
            ];
        })->toArray();

        $this->table(
            ['Nama', 'Ukuran', 'Kualitas', 'Suffix'],
            $sizeData
        );

        return 0;
    }

    protected function batchGenerate(string $filePath): int
    {
        if (!$filePath) {
            $this->error('❌ Option --file diperlukan untuk action batch');
            return 1;
        }

        // Cek apakah file ada
        if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
            $this->error("❌ File tidak ditemukan: {$filePath}");
            return 1;
        }

        $this->info("🔄 Batch generating thumbnails untuk file: {$filePath}");

        $this->info("📸 Generating thumbnail...");
        $thumbnailPath = $this->thumbnailService->generateThumbnail($filePath, 'small');
        
        if ($thumbnailPath) {
            $this->info("✅ Thumbnail berhasil dibuat: {$thumbnailPath}");
            
            // Cek apakah file thumbnail ada
            if (Storage::disk($this->configService->getStorageDisk())->exists($thumbnailPath)) {
                $this->info("✅ File thumbnail ada di storage");
                
                // Tampilkan info file
                $size = Storage::disk($this->configService->getStorageDisk())->size($thumbnailPath);
                $this->info("📊 Ukuran thumbnail: " . number_format($size / 1024, 2) . " KB");
                
                // Tampilkan URL
                $url = $this->configService->generateThumbnailUrl($filePath, 'small');
                $this->info("🌐 URL thumbnail: {$url}");
            } else {
                $this->error("❌ File thumbnail tidak ada di storage");
            }
        } else {
            $this->error("❌ Gagal membuat thumbnail");
            return 1;
        }

        return 0;
    }

    /**
     * Test thumbnail generation
     */
    protected function testThumbnail(string $filePath): void
    {
        $this->info("🧪 Testing thumbnail generation untuk: {$filePath}");
        
        try {
            // Cek apakah file ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
                $this->error("❌ File tidak ditemukan: {$filePath}");
                return;
            }

            $this->info("✅ File ditemukan");

            // Test generate thumbnail
            $this->info("📸 Generating thumbnail...");
            $thumbnailPath = $this->thumbnailService->generateThumbnail($filePath, 'small');
            
            if ($thumbnailPath) {
                $this->info("✅ Thumbnail berhasil dibuat: {$thumbnailPath}");
                
                // Cek apakah file thumbnail ada
                if (Storage::disk($this->configService->getStorageDisk())->exists($thumbnailPath)) {
                    $this->info("✅ File thumbnail ada di storage");
                    
                    // Tampilkan info file
                    $size = Storage::disk($this->configService->getStorageDisk())->size($thumbnailPath);
                    $this->info("📊 Ukuran thumbnail: " . number_format($size / 1024, 2) . " KB");
                    
                    // Tampilkan URL
                    $url = $this->configService->generateThumbnailUrl($filePath, 'small');
                    $this->info("🌐 URL thumbnail: {$url}");
                } else {
                    $this->error("❌ File thumbnail tidak ada di storage");
                }
            } else {
                $this->error("❌ Gagal membuat thumbnail");
            }

            // Test WebP jika diaktifkan
            if ($this->configService->isThumbnailWebpEnabled()) {
                $this->info("🔄 Testing WebP conversion...");
                $webpPath = $this->thumbnailService->generateThumbnail($filePath, 'small');
                
                if ($webpPath) {
                    $this->info("✅ WebP thumbnail berhasil dibuat");
                } else {
                    $this->error("❌ Gagal membuat WebP thumbnail");
                }
            }

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
        }
    }

    protected function showHelp()
    {
        $this->info("Actions yang tersedia: generate, regenerate, cleanup, config, batch, test");
    }
} 