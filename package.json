{"name": "hotel", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:install": "playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.7", "autoprefixer": "^10.4.16", "axios": "^1.6.1", "browser-sync": "^3.0.3", "concurrently": "^9.1.2", "cropperjs": "^2.0.0", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.4.32", "postcss-nesting": "^13.0.1", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}, "type": "module", "dependencies": {"grapesjs": "^0.22.7", "grapesjs-component-code-editor": "^1.0.20", "hmr": "^0.1.0"}}