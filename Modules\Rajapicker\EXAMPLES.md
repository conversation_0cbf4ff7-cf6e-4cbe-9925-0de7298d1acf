# Contoh Penggunaan RajaPicker dengan Konfigurasi

## 1. <PERSON><PERSON><PERSON><PERSON>ar

### Menggunakan RajaPicker Original
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

// Penggunaan dasar
RajaPicker::make('image')
    ->label('<PERSON>lih Gambar')
    ->collection('default');

// Multiple selection
RajaPicker::make('gallery')
    ->label('Galeri Gambar')
    ->multiple()
    ->collection('gallery');

// Filter berdasarkan user
RajaPicker::make('user_image')
    ->label('Gambar Saya')
    ->byUser()
    ->collection('user_images');
```

### Menggunakan RajaPickerWithConfig (Menggunakan Konfigurasi)
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;

// Penggunaan dengan konfigurasi otomatis
RajaPickerWithConfig::make('product_image')
    ->label('Gambar Produk')
    ->collection('products'); // Akan menggunakan konfigurasi dari config.php

// Dengan tema tertentu
RajaPickerWithConfig::make('banner_image')
    ->label('Banner Image')
    ->collection('banners')
    ->theme('compact');

// Multiple dengan konfigurasi
RajaPickerWithConfig::make('gallery_images')
    ->label('Galeri Gambar')
    ->collection('gallery')
    ->multiple();
```

## 2. Contoh Resource Filament

### Product Resource
```php
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Produk')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Produk')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('price')
                            ->label('Harga')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Gambar Produk')
                    ->schema([
                        // Menggunakan konfigurasi dari config.php
                        RajaPickerWithConfig::make('main_image')
                            ->label('Gambar Utama')
                            ->collection('products')
                            ->required(),
                        
                        // Multiple images untuk galeri
                        RajaPickerWithConfig::make('gallery_images')
                            ->label('Galeri Gambar')
                            ->collection('gallery')
                            ->multiple()
                            ->theme('compact'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Produk')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('price')
                    ->label('Harga')
                    ->money('IDR')
                    ->sortable(),
                
                Tables\Columns\ImageColumn::make('main_image')
                    ->label('Gambar')
                    ->size(50),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
```

### Banner Resource
```php
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Banner')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Judul Banner')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('subtitle')
                            ->label('Subtitle')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('button_text')
                            ->label('Teks Tombol')
                            ->maxLength(100),
                        
                        Forms\Components\UrlInput::make('button_url')
                            ->label('URL Tombol'),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Gambar Banner')
                    ->schema([
                        // Menggunakan collection banners dengan ukuran file yang lebih besar
                        RajaPickerWithConfig::make('image')
                            ->label('Gambar Banner')
                            ->collection('banners')
                            ->theme('detailed') // Tema dengan preview yang lebih besar
                            ->required(),
                        
                        // Gambar mobile (opsional)
                        RajaPickerWithConfig::make('mobile_image')
                            ->label('Gambar Mobile (Opsional)')
                            ->collection('banners')
                            ->theme('detailed'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Judul')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\ImageColumn::make('image')
                    ->label('Gambar')
                    ->size(80),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
        ];
    }
}
```

## 3. Contoh Livewire Component

### Image Gallery Component
```php
<?php

namespace App\Livewire;

use Livewire\Component;
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;

class ImageGallery extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                RajaPickerWithConfig::make('gallery_images')
                    ->label('Pilih Gambar untuk Galeri')
                    ->collection('gallery')
                    ->multiple()
                    ->theme('compact')
                    ->required(),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        
        // Proses penyimpanan data
        // $data['gallery_images'] akan berisi array URL gambar
        
        $this->dispatch('gallery-saved', images: $data['gallery_images']);
    }

    public function render()
    {
        return view('livewire.image-gallery');
    }
}
```

## 4. Contoh Service Class

### Image Processing Service
```php
<?php

namespace App\Services;

use Modules\Rajapicker\Services\RajaPickerConfigService;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ImageProcessingService
{
    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        $this->configService = new RajaPickerConfigService();
    }

    /**
     * Process uploaded image berdasarkan konfigurasi collection
     */
    public function processImage(string $imagePath, string $collectionName = 'default'): array
    {
        $collectionConfig = $this->configService->getCollectionConfig($collectionName);
        $webpConfig = $this->configService->getWebpConfig();
        $thumbnailConfig = $this->configService->getThumbnailConfig();

        $results = [
            'original' => $imagePath,
            'thumbnails' => [],
            'webp' => null,
        ];

        // Generate thumbnails jika diaktifkan
        if ($this->configService->isThumbnailEnabled()) {
            $results['thumbnails'] = $this->generateThumbnails($imagePath, $thumbnailConfig['sizes']);
        }

        // Convert to WebP jika diaktifkan
        if ($this->configService->isWebpEnabled()) {
            $results['webp'] = $this->convertToWebp($imagePath, $webpConfig['quality']);
        }

        return $results;
    }

    /**
     * Generate thumbnails dengan ukuran yang berbeda
     */
    protected function generateThumbnails(string $imagePath, array $sizes): array
    {
        $thumbnails = [];
        
        foreach ($sizes as $sizeName => $dimensions) {
            $thumbnailPath = $this->createThumbnail($imagePath, $dimensions[0], $dimensions[1], $sizeName);
            $thumbnails[$sizeName] = $thumbnailPath;
        }

        return $thumbnails;
    }

    /**
     * Create thumbnail dengan ukuran tertentu
     */
    protected function createThumbnail(string $imagePath, int $width, int $height, string $suffix): string
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$suffix}." . $pathInfo['extension'];

        $image = Image::make(Storage::path($imagePath));
        $image->fit($width, $height);
        $image->save(Storage::path($thumbnailPath));

        return $thumbnailPath;
    }

    /**
     * Convert image to WebP
     */
    protected function convertToWebp(string $imagePath, int $quality): string
    {
        $pathInfo = pathinfo($imagePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';

        $image = Image::make(Storage::path($imagePath));
        $image->encode('webp', $quality);
        $image->save(Storage::path($webpPath));

        return $webpPath;
    }

    /**
     * Validate image berdasarkan konfigurasi collection
     */
    public function validateImage($file, string $collectionName = 'default'): array
    {
        $collectionConfig = $this->configService->getCollectionConfig($collectionName);
        $securityConfig = $this->configService->getSecurityConfig();

        $errors = [];

        // Check file size
        $maxSize = $this->configService->getMaxFileSizeBytes($collectionName);
        if ($file->getSize() > $maxSize) {
            $errors[] = "Ukuran file tidak boleh lebih dari " . ($maxSize / 1024 / 1024) . "MB";
        }

        // Check file type
        $acceptedTypes = $this->configService->getAcceptedFileTypes($collectionName);
        if (!in_array($file->getMimeType(), $acceptedTypes)) {
            $errors[] = "Tipe file tidak didukung. Tipe yang diterima: " . implode(', ', $acceptedTypes);
        }

        // Check file extension
        $allowedExtensions = $securityConfig['allowed_extensions'];
        $fileExtension = strtolower($file->getClientOriginalExtension());
        if (!in_array($fileExtension, $allowedExtensions)) {
            $errors[] = "Ekstensi file tidak diizinkan. Ekstensi yang diizinkan: " . implode(', ', $allowedExtensions);
        }

        return $errors;
    }
}
```

## 5. Contoh Controller

### Media Controller
```php
<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Rajapicker\Services\RajaPickerConfigService;
use App\Services\ImageProcessingService;

class MediaController extends Controller
{
    protected RajaPickerConfigService $configService;
    protected ImageProcessingService $imageService;

    public function __construct()
    {
        $this->configService = new RajaPickerConfigService();
        $this->imageService = new ImageProcessingService();
    }

    /**
     * Upload image dengan validasi berdasarkan collection
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file',
            'collection' => 'required|string',
        ]);

        $file = $request->file('file');
        $collectionName = $request->input('collection');

        // Validasi collection
        if (!$this->configService->hasCollection($collectionName)) {
            return response()->json([
                'success' => false,
                'message' => 'Collection tidak ditemukan'
            ], 400);
        }

        // Validasi file
        $validationErrors = $this->imageService->validateImage($file, $collectionName);
        if (!empty($validationErrors)) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validationErrors
            ], 400);
        }

        // Upload file
        $path = $file->store($this->configService->getDirectory($collectionName), 'public');

        // Process image
        $processedImages = $this->imageService->processImage($path, $collectionName);

        return response()->json([
            'success' => true,
            'message' => 'File berhasil diupload',
            'data' => [
                'path' => $path,
                'url' => Storage::url($path),
                'processed' => $processedImages,
            ]
        ]);
    }

    /**
     * Get available collections
     */
    public function collections()
    {
        $collections = $this->configService->getAllCollections();
        
        return response()->json([
            'success' => true,
            'data' => $collections
        ]);
    }

    /**
     * Get collection configuration
     */
    public function collectionConfig(string $collectionName)
    {
        $config = $this->configService->getCollectionConfig($collectionName);
        
        if (!$config) {
            return response()->json([
                'success' => false,
                'message' => 'Collection tidak ditemukan'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $config
        ]);
    }
}
```

## 6. Contoh Blade View

### Gallery View
```blade
@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Galeri Gambar</h1>
        <p class="text-gray-600 mt-2">Pilih dan kelola gambar untuk galeri</p>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form wire:submit.prevent="save">
            {{ $this->form }}
            
            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Simpan Galeri
                </button>
            </div>
        </form>
    </div>

    @if($data['gallery_images'] ?? false)
    <div class="mt-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Gambar yang Dipilih</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @foreach($data['gallery_images'] as $image)
            <div class="relative group">
                <img src="{{ $image }}" alt="Gallery Image" class="w-full h-32 object-cover rounded-lg">
                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                    <button type="button" class="text-white hover:text-red-400" wire:click="removeImage('{{ $image }}')">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
    // Listen untuk event gallery-saved
    window.addEventListener('gallery-saved', event => {
        console.log('Gallery saved:', event.detail.images);
        // Tampilkan notifikasi atau redirect
    });
</script>
@endpush
@endsection
```

## 7. Contoh Testing

### RajaPicker Test
```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Modules\Rajapicker\Services\RajaPickerConfigService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RajaPickerTest extends TestCase
{
    use RefreshDatabase;

    protected RajaPickerConfigService $configService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->configService = new RajaPickerConfigService();
    }

    /** @test */
    public function it_can_load_default_configuration()
    {
        $defaults = $this->configService->getDefaults();
        
        $this->assertIsArray($defaults);
        $this->assertArrayHasKey('accepted_file_types', $defaults);
        $this->assertArrayHasKey('max_file_size', $defaults);
        $this->assertArrayHasKey('collection', $defaults);
    }

    /** @test */
    public function it_can_load_collection_configuration()
    {
        $productConfig = $this->configService->getCollectionConfig('products');
        
        $this->assertIsArray($productConfig);
        $this->assertEquals('Product Images', $productConfig['name']);
        $this->assertEquals(5, $productConfig['max_file_size']);
    }

    /** @test */
    public function it_can_validate_collection_exists()
    {
        $this->assertTrue($this->configService->hasCollection('products'));
        $this->assertFalse($this->configService->hasCollection('non_existent'));
    }

    /** @test */
    public function it_can_get_accepted_file_types_for_collection()
    {
        $fileTypes = $this->configService->getAcceptedFileTypes('products');
        
        $this->assertIsArray($fileTypes);
        $this->assertContains('image/jpeg', $fileTypes);
        $this->assertContains('image/png', $fileTypes);
        $this->assertContains('image/webp', $fileTypes);
    }

    /** @test */
    public function it_can_get_max_file_size_for_collection()
    {
        $maxSize = $this->configService->getMaxFileSize('products');
        
        $this->assertEquals(5, $maxSize);
    }

    /** @test */
    public function it_can_get_file_size_in_bytes()
    {
        $maxSizeBytes = $this->configService->getMaxFileSizeBytes('products');
        
        $this->assertEquals(5 * 1024 * 1024, $maxSizeBytes);
    }

    /** @test */
    public function it_can_get_accepted_file_types_string()
    {
        $fileTypesString = $this->configService->getAcceptedFileTypesString('products');
        
        $this->assertStringContainsString('.jpg', $fileTypesString);
        $this->assertStringContainsString('.png', $fileTypesString);
        $this->assertStringContainsString('.webp', $fileTypesString);
    }

    /** @test */
    public function it_can_get_theme_configuration()
    {
        $themeConfig = $this->configService->getThemeConfig('compact');
        
        $this->assertIsArray($themeConfig);
        $this->assertEquals(100, $themeConfig['preview_size']);
        $this->assertEquals(6, $themeConfig['grid_columns']);
    }

    /** @test */
    public function it_can_get_performance_configuration()
    {
        $performanceConfig = $this->configService->getPerformanceConfig();
        
        $this->assertIsArray($performanceConfig);
        $this->assertArrayHasKey('cache_media_list', $performanceConfig);
        $this->assertArrayHasKey('cache_ttl', $performanceConfig);
    }

    /** @test */
    public function it_can_get_security_configuration()
    {
        $securityConfig = $this->configService->getSecurityConfig();
        
        $this->assertIsArray($securityConfig);
        $this->assertArrayHasKey('allowed_extensions', $securityConfig);
        $this->assertArrayHasKey('max_file_size_bytes', $securityConfig);
    }
}
```

## 8. Contoh Artisan Command

### RajaPicker Command
```php
<?php

namespace Modules\Rajapicker\Console\Commands;

use Illuminate\Console\Command;
use Modules\Rajapicker\Services\RajaPickerConfigService;

class RajaPickerCommand extends Command
{
    protected $signature = 'rajapicker:config {action} {--collection=} {--theme=}';
    protected $description = 'Manage RajaPicker configuration';

    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        parent::__construct();
        $this->configService = new RajaPickerConfigService();
    }

    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list-collections':
                $this->listCollections();
                break;
            case 'show-collection':
                $this->showCollection();
                break;
            case 'list-themes':
                $this->listThemes();
                break;
            case 'show-theme':
                $this->showTheme();
                break;
            case 'validate':
                $this->validateConfig();
                break;
            default:
                $this->error("Action '{$action}' tidak dikenal");
                $this->info("Actions yang tersedia: list-collections, show-collection, list-themes, show-theme, validate");
        }
    }

    protected function listCollections()
    {
        $collections = $this->configService->getAllCollections();
        
        $this->info('Daftar Collections:');
        $this->table(
            ['Nama', 'Deskripsi', 'Max Size (MB)', 'Directory'],
            collect($collections)->map(function ($config, $name) {
                return [
                    $name,
                    $config['description'] ?? '-',
                    $config['max_file_size'] ?? '-',
                    $config['directory'] ?? '-',
                ];
            })->toArray()
        );
    }

    protected function showCollection()
    {
        $collectionName = $this->option('collection');
        
        if (!$collectionName) {
            $this->error('Option --collection diperlukan');
            return;
        }

        $config = $this->configService->getCollectionConfig($collectionName);
        
        if (!$config) {
            $this->error("Collection '{$collectionName}' tidak ditemukan");
            return;
        }

        $this->info("Konfigurasi Collection: {$collectionName}");
        $this->table(
            ['Property', 'Value'],
            collect($config)->map(function ($value, $key) {
                return [
                    $key,
                    is_array($value) ? json_encode($value) : $value,
                ];
            })->toArray()
        );
    }

    protected function listThemes()
    {
        $themes = $this->configService->getAllThemes();
        
        $this->info('Daftar Themes:');
        $this->table(
            ['Nama', 'Preview Size', 'Grid Columns', 'Show File Info'],
            collect($themes)->map(function ($config, $name) {
                return [
                    $name,
                    $config['preview_size'] ?? '-',
                    $config['grid_columns'] ?? '-',
                    $config['show_file_info'] ? 'Yes' : 'No',
                ];
            })->toArray()
        );
    }

    protected function showTheme()
    {
        $themeName = $this->option('theme');
        
        if (!$themeName) {
            $this->error('Option --theme diperlukan');
            return;
        }

        $config = $this->configService->getThemeConfig($themeName);
        
        if (!$config) {
            $this->error("Theme '{$themeName}' tidak ditemukan");
            return;
        }

        $this->info("Konfigurasi Theme: {$themeName}");
        $this->table(
            ['Property', 'Value'],
            collect($config)->map(function ($value, $key) {
                return [
                    $key,
                    is_bool($value) ? ($value ? 'Yes' : 'No') : $value,
                ];
            })->toArray()
        );
    }

    protected function validateConfig()
    {
        $this->info('Validasi Konfigurasi RajaPicker...');
        
        $errors = [];
        
        // Validate collections
        $collections = $this->configService->getAllCollections();
        foreach ($collections as $name => $config) {
            if (!isset($config['name'])) {
                $errors[] = "Collection '{$name}' tidak memiliki property 'name'";
            }
            if (!isset($config['accepted_file_types']) || !is_array($config['accepted_file_types'])) {
                $errors[] = "Collection '{$name}' tidak memiliki property 'accepted_file_types' yang valid";
            }
            if (!isset($config['max_file_size']) || !is_numeric($config['max_file_size'])) {
                $errors[] = "Collection '{$name}' tidak memiliki property 'max_file_size' yang valid";
            }
        }
        
        // Validate themes
        $themes = $this->configService->getAllThemes();
        foreach ($themes as $name => $config) {
            if (!isset($config['preview_size']) || !is_numeric($config['preview_size'])) {
                $errors[] = "Theme '{$name}' tidak memiliki property 'preview_size' yang valid";
            }
            if (!isset($config['grid_columns']) || !is_numeric($config['grid_columns'])) {
                $errors[] = "Theme '{$name}' tidak memiliki property 'grid_columns' yang valid";
            }
        }
        
        if (empty($errors)) {
            $this->info('✅ Konfigurasi valid!');
        } else {
            $this->error('❌ Konfigurasi memiliki error:');
            foreach ($errors as $error) {
                $this->line("  - {$error}");
            }
        }
    }
}
```

## 9. Contoh Middleware

### RajaPicker Middleware
```php
<?php

namespace Modules\Rajapicker\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Rajapicker\Services\RajaPickerConfigService;

class RajaPickerMiddleware
{
    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        $this->configService = new RajaPickerConfigService();
    }

    public function handle(Request $request, Closure $next)
    {
        // Share konfigurasi ke view jika diperlukan
        view()->share('rajapickerConfig', [
            'defaults' => $this->configService->getDefaults(),
            'collections' => $this->configService->getAllCollections(),
            'themes' => $this->configService->getAllThemes(),
        ]);

        return $next($request);
    }
}
```

## 10. Contoh Helper Function

### RajaPicker Helper
```php
<?php

if (!function_exists('rajapicker_config')) {
    /**
     * Get RajaPicker configuration
     */
    function rajapicker_config(string $key = null, $default = null)
    {
        $configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
        
        if ($key === null) {
            return $configService;
        }
        
        return data_get($configService->getDefaults(), $key, $default);
    }
}

if (!function_exists('rajapicker_collection')) {
    /**
     * Get RajaPicker collection configuration
     */
    function rajapicker_collection(string $collectionName)
    {
        $configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
        return $configService->getCollectionConfig($collectionName);
    }
}

if (!function_exists('rajapicker_theme')) {
    /**
     * Get RajaPicker theme configuration
     */
    function rajapicker_theme(string $themeName)
    {
        $configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
        return $configService->getThemeConfig($themeName);
    }
}
```

Dengan contoh-contoh di atas, Anda dapat menggunakan RajaPicker dengan konfigurasi yang fleksibel dan mudah dikelola. Konfigurasi dapat disesuaikan sesuai kebutuhan aplikasi tanpa perlu mengubah kode komponen. 