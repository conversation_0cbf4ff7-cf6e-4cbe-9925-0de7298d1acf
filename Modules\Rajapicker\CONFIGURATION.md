# RajaPicker Configuration Guide

## Overview

RajaPicker adalah komponen Filament untuk memilih dan mengupload gambar dengan interface yang user-friendly. File konfigurasi `config.php` memungkinkan Anda untuk menyesuaikan berbagai pengaturan sesuai kebutuhan aplikasi.

## Struktur Konfigurasi

### 1. Defaults
Pengaturan default yang akan digunakan jika tidak ada konfigurasi khusus yang diberikan:

```php
'defaults' => [
    'accepted_file_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    'max_file_size' => 10, // MB
    'collection' => 'default',
    'multiple' => false,
    'directory' => null,
    'features' => [
        'picker' => true,
        'uploader' => true,
    ],
    'ui' => [
        'placeholder' => 'Pilih atau upload gambar...',
        'preview_size' => 150,
        'show_file_name' => true,
        'show_file_size' => true,
    ],
    'per_page' => 12,
    'user_filter' => [
        'by_user' => false,
        'by_user_id' => null,
    ],
    'convert_webp' => false,
]
```

### 2. Collections
Konfigurasi untuk berbagai collection yang dapat digunakan:

```php
'collections' => [
    'default' => [
        'name' => 'Default Collection',
        'description' => 'Collection default untuk media umum',
        'accepted_file_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'max_file_size' => 10,
        'directory' => null,
    ],
    'products' => [
        'name' => 'Product Images',
        'description' => 'Collection untuk gambar produk',
        'accepted_file_types' => ['image/jpeg', 'image/png', 'image/webp'],
        'max_file_size' => 5,
        'directory' => 'products',
    ],
    // ... collection lainnya
]
```

### 3. Validation
Aturan validasi untuk upload file:

```php
'validation' => [
    'rules' => [
        'file' => 'required|file|mimes:jpeg,jpg,png,gif,webp|max:10240',
    ],
    'messages' => [
        'file.required' => 'File gambar harus dipilih.',
        'file.file' => 'File yang diupload harus berupa file yang valid.',
        'file.mimes' => 'File harus berupa gambar dengan format: jpeg, jpg, png, gif, atau webp.',
        'file.max' => 'Ukuran file tidak boleh lebih dari 10MB.',
    ],
]
```

### 4. Storage
Konfigurasi penyimpanan file:

```php
'storage' => [
    'disk' => 'public',
    'path' => 'rajapicker',
    'url_prefix' => '/storage',
    'webp' => [
        'enabled' => false,
        'quality' => 85,
        'preserve_original' => true,
    ],
]
```

### 5. UI Themes
Konfigurasi tema untuk interface:

```php
'themes' => [
    'default' => [
        'preview_size' => 150,
        'grid_columns' => 4,
        'show_file_info' => true,
        'show_upload_progress' => true,
    ],
    'compact' => [
        'preview_size' => 100,
        'grid_columns' => 6,
        'show_file_info' => false,
        'show_upload_progress' => true,
    ],
    'detailed' => [
        'preview_size' => 200,
        'grid_columns' => 3,
        'show_file_info' => true,
        'show_upload_progress' => true,
    ],
]
```

### 6. Performance
Pengaturan untuk optimasi performa:

```php
'performance' => [
    'cache_media_list' => true,
    'cache_ttl' => 300, // 5 menit
    'lazy_loading' => true,
    'max_media_per_page' => 200,
    'thumbnail_generation' => [
        'enabled' => true,
        'sizes' => [
            'thumb' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600],
        ],
    ],
]
```

### 7. Security
Pengaturan keamanan:

```php
'security' => [
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'max_file_size_bytes' => 10485760, // 10MB
    'scan_for_viruses' => false,
    'validate_mime_type' => true,
    'prevent_duplicate_uploads' => true,
]
```

## Cara Penggunaan

### 1. Menggunakan Default Configuration
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

RajaPicker::make('image')
    ->label('Pilih Gambar')
```

### 2. Menggunakan Collection Khusus
```php
RajaPicker::make('product_image')
    ->collection('products')
    ->label('Gambar Produk')
```

### 3. Multiple Selection
```php
RajaPicker::make('gallery_images')
    ->multiple()
    ->collection('gallery')
    ->label('Galeri Gambar')
```

### 4. Filter Berdasarkan User
```php
RajaPicker::make('user_image')
    ->byUser() // Filter berdasarkan user yang sedang login
    ->label('Gambar Saya')
```

### 5. Custom File Types
```php
RajaPicker::make('banner_image')
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->maxFileSize(15)
    ->label('Banner Image')
```

### 6. WebP Conversion
```php
RajaPicker::make('optimized_image')
    ->convertWebp()
    ->label('Gambar Optimized')
```

## Akses Konfigurasi dalam Kode

### Menggunakan Config Helper
```php
// Mengakses default configuration
$defaultFileTypes = config('rajapicker.defaults.accepted_file_types');
$maxFileSize = config('rajapicker.defaults.max_file_size');

// Mengakses collection configuration
$productCollection = config('rajapicker.collections.products');

// Mengakses validation rules
$validationRules = config('rajapicker.validation.rules');
```

### Menggunakan Config dalam Service
```php
use Illuminate\Support\Facades\Config;

class RajaPickerService
{
    public function getDefaultConfig()
    {
        return Config::get('rajapicker.defaults');
    }
    
    public function getCollectionConfig($collectionName)
    {
        return Config::get("rajapicker.collections.{$collectionName}");
    }
}
```

## Customization

### Menambah Collection Baru
Tambahkan collection baru di `config.php`:

```php
'collections' => [
    // ... existing collections
    'blog' => [
        'name' => 'Blog Images',
        'description' => 'Collection untuk gambar blog',
        'accepted_file_types' => ['image/jpeg', 'image/png', 'image/webp'],
        'max_file_size' => 8,
        'directory' => 'blog',
    ],
]
```

### Menambah Tema Baru
```php
'themes' => [
    // ... existing themes
    'minimal' => [
        'preview_size' => 80,
        'grid_columns' => 8,
        'show_file_info' => false,
        'show_upload_progress' => false,
    ],
]
```

## Best Practices

1. **Gunakan Collection yang Spesifik**: Buat collection yang sesuai dengan kebutuhan aplikasi
2. **Batasi Ukuran File**: Sesuaikan `max_file_size` dengan kebutuhan dan kapasitas server
3. **Aktifkan WebP Conversion**: Untuk optimasi performa website
4. **Gunakan User Filter**: Untuk aplikasi multi-user
5. **Cache Media List**: Untuk performa yang lebih baik
6. **Validasi File**: Pastikan validasi yang ketat untuk keamanan

## Troubleshooting

### File Tidak Terupload
- Periksa permission folder storage
- Pastikan disk storage dikonfigurasi dengan benar
- Cek ukuran file tidak melebihi batas

### Collection Tidak Muncul
- Pastikan collection sudah didefinisikan di config
- Cek apakah ada media di collection tersebut
- Periksa filter user jika menggunakan `byUser()`

### Performance Lambat
- Aktifkan cache media list
- Kurangi `max_media_per_page`
- Gunakan lazy loading
- Optimasi thumbnail generation 