<?php

namespace Modules\Rajapicker\Services;

use Illuminate\Support\Facades\Config;

class RajaPickerConfigService
{
    /**
     * Mendapatkan konfigurasi default
     */
    public function getDefaults(): array
    {
        return Config::get('rajapicker.defaults', []);
    }

    /**
     * Mendapatkan konfigurasi collection tertentu
     */
    public function getCollectionConfig(string $collectionName): ?array
    {
        return Config::get("rajapicker.collections.{$collectionName}");
    }

    /**
     * Mendapatkan semua collection yang tersedia
     */
    public function getAllCollections(): array
    {
        return Config::get('rajapicker.collections', []);
    }

    /**
     * Mendapatkan konfigurasi tema tertentu
     */
    public function getThemeConfig(string $themeName): ?array
    {
        return Config::get("rajapicker.themes.{$themeName}");
    }

    /**
     * Mendapatkan semua tema yang tersedia
     */
    public function getAllThemes(): array
    {
        return Config::get('rajapicker.themes', []);
    }

    /**
     * Mendapatkan aturan validasi
     */
    public function getValidationRules(): array
    {
        return Config::get('rajapicker.validation.rules', []);
    }

    /**
     * Mendapatkan pesan validasi
     */
    public function getValidationMessages(): array
    {
        return Config::get('rajapicker.validation.messages', []);
    }

    /**
     * Mendapatkan konfigurasi storage
     */
    public function getStorageConfig(): array
    {
        return Config::get('rajapicker.storage', []);
    }

    /**
     * Mendapatkan konfigurasi performa
     */
    public function getPerformanceConfig(): array
    {
        return Config::get('rajapicker.performance', []);
    }

    /**
     * Mendapatkan konfigurasi keamanan
     */
    public function getSecurityConfig(): array
    {
        return Config::get('rajapicker.security', []);
    }

    /**
     * Mendapatkan tipe file yang diterima untuk collection tertentu
     */
    public function getAcceptedFileTypes(string $collectionName = 'default'): array
    {
        $collectionConfig = $this->getCollectionConfig($collectionName);
        
        if ($collectionConfig && isset($collectionConfig['accepted_file_types'])) {
            return $collectionConfig['accepted_file_types'];
        }

        return Config::get('rajapicker.defaults.accepted_file_types', []);
    }

    /**
     * Mendapatkan ukuran maksimal file untuk collection tertentu
     */
    public function getMaxFileSize(string $collectionName = 'default'): int
    {
        $collectionConfig = $this->getCollectionConfig($collectionName);
        
        if ($collectionConfig && isset($collectionConfig['max_file_size'])) {
            return $collectionConfig['max_file_size'];
        }

        return Config::get('rajapicker.defaults.max_file_size', 10);
    }

    /**
     * Mendapatkan direktori untuk collection tertentu
     */
    public function getDirectory(string $collectionName = 'default'): ?string
    {
        $collectionConfig = $this->getCollectionConfig($collectionName);
        
        if ($collectionConfig && isset($collectionConfig['directory'])) {
            return $collectionConfig['directory'];
        }

        return Config::get('rajapicker.defaults.directory');
    }

    /**
     * Mengecek apakah collection ada
     */
    public function hasCollection(string $collectionName): bool
    {
        return !is_null($this->getCollectionConfig($collectionName));
    }

    /**
     * Mendapatkan daftar nama collection
     */
    public function getCollectionNames(): array
    {
        return array_keys($this->getAllCollections());
    }

    /**
     * Mendapatkan konfigurasi lengkap untuk RajaPicker field
     */
    public function getFieldConfig(string $collectionName = 'default', array $overrides = []): array
    {
        $defaults = $this->getDefaults();
        $collectionConfig = $this->getCollectionConfig($collectionName);
        
        $config = array_merge($defaults, $collectionConfig ?? []);
        
        return array_merge($config, $overrides);
    }

    /**
     * Mendapatkan string tipe file yang diterima untuk HTML input
     */
    public function getAcceptedFileTypesString(string $collectionName = 'default'): string
    {
        $fileTypes = $this->getAcceptedFileTypes($collectionName);
        
        // Konversi MIME types ke ekstensi file
        $extensions = [];
        foreach ($fileTypes as $mimeType) {
            switch ($mimeType) {
                case 'image/jpeg':
                    $extensions[] = '.jpg,.jpeg';
                    break;
                case 'image/png':
                    $extensions[] = '.png';
                    break;
                case 'image/gif':
                    $extensions[] = '.gif';
                    break;
                case 'image/webp':
                    $extensions[] = '.webp';
                    break;
            }
        }
        
        return implode(',', $extensions);
    }

    /**
     * Mendapatkan ukuran maksimal file dalam bytes
     */
    public function getMaxFileSizeBytes(string $collectionName = 'default'): int
    {
        $maxFileSizeMB = $this->getMaxFileSize($collectionName);
        return $maxFileSizeMB * 1024 * 1024;
    }

    /**
     * Mendapatkan konfigurasi WebP
     */
    public function getWebpConfig(): array
    {
        return Config::get('rajapicker.storage.webp', []);
    }

    /**
     * Mengecek apakah WebP conversion diaktifkan
     */
    public function isWebpEnabled(): bool
    {
        $webpConfig = $this->getWebpConfig();
        return $webpConfig['enabled'] ?? false;
    }

    /**
     * Mendapatkan kualitas WebP
     */
    public function getWebpQuality(): int
    {
        $webpConfig = $this->getWebpConfig();
        return $webpConfig['quality'] ?? 85;
    }

    /**
     * Mengecek apakah original file harus dipertahankan
     */
    public function shouldPreserveOriginal(): bool
    {
        $webpConfig = $this->getWebpConfig();
        return $webpConfig['preserve_original'] ?? true;
    }

    /**
     * Mendapatkan konfigurasi thumbnail
     */
    public function getThumbnailConfig(): array
    {
        $storageConfig = $this->getStorageConfig();
        return $storageConfig['thumbnail'] ?? [];
    }

    /**
     * Mengecek apakah thumbnail generation diaktifkan
     */
    public function isThumbnailEnabled(): bool
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        return $thumbnailConfig['enabled'] ?? false;
    }

    /**
     * Mendapatkan ukuran thumbnail yang tersedia
     */
    public function getThumbnailSizes(): array
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        $sizes = $thumbnailConfig['sizes'] ?? [];

        // Filter hanya yang enabled === true (atau tidak di-set => true)
        return array_filter($sizes, function ($item) {
            return !isset($item['enabled']) || $item['enabled'] === true;
        });
    }

    /**
     * Mendapatkan direktori thumbnail
     */
    public function getThumbnailDirectory(): string
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        return $thumbnailConfig['directory'] ?? 'thumbnails';
    }

    /**
     * Mendapatkan prefix thumbnail
     */
    public function getThumbnailPrefix(): string
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        return $thumbnailConfig['prefix'] ?? '{name}_thumb';
    }

    /**
     * Mengecek apakah WebP conversion untuk thumbnail diaktifkan
     */
    public function isThumbnailWebpEnabled(): bool
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        return $thumbnailConfig['webp_conversion'] ?? false;
    }

    /**
     * Mendapatkan konfigurasi ukuran thumbnail tertentu
     */
    public function getThumbnailSizeConfig(string $sizeName): ?array
    {
        $sizes = $this->getThumbnailSizes();
        return $sizes[$sizeName] ?? null;
    }

    /**
     * Mendapatkan daftar nama ukuran thumbnail
     */
    public function getThumbnailSizeNames(): array
    {
        return array_keys($this->getThumbnailSizes());
    }

    /**
     * Generate nama file thumbnail
     */
    public function generateThumbnailName(string $originalName, string $sizeName): string
    {
        $thumbnailConfig = $this->getThumbnailConfig();
        $prefix = $thumbnailConfig['prefix'] ?? '{name}_thumb';
        
        $pathInfo = pathinfo($originalName);
        $name = $pathInfo['filename'];
        $extension = strtolower($pathInfo['extension'] ?? 'jpg'); // Default ke jpg jika tidak ada ekstensi
        
        // Replace {name} dengan nama file asli
        $thumbnailName = str_replace('{name}', $name, $prefix);
        
        // Tambahkan suffix ukuran
        $sizeConfig = $this->getThumbnailSizeConfig($sizeName);
        if ($sizeConfig && isset($sizeConfig['suffix'])) {
            $thumbnailName .= '_' . $sizeConfig['suffix'];
        }
        
        // Pastikan ekstensi yang valid
        $validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        if (!in_array($extension, $validExtensions)) {
            $extension = 'jpg'; // Default ke jpg jika ekstensi tidak valid
        }
        
        // Tambahkan ekstensi
        $thumbnailName .= '.' . $extension;
        
        return $thumbnailName;
    }

    /**
     * Generate path lengkap untuk thumbnail
     */
    public function generateThumbnailPath(string $originalPath, string $sizeName): string
    {
        // Direktori file asli (mis. uploads/cms)
        $baseDir = dirname($originalPath);

        // Sub-folder thumbnail sesuai config (default 'thumbnails')
        $thumbDirName = $this->getThumbnailDirectory();

        // Gabungkan: uploads/cms/thumbnails
        $thumbnailDir = rtrim($baseDir, '/') . '/' . ltrim($thumbDirName, '/');

        $thumbnailName = $this->generateThumbnailName(basename($originalPath), $sizeName);

        $thumbnailPath = $thumbnailDir . '/' . $thumbnailName;
        return str_replace('\\', '/', $thumbnailPath);
    }

    /**
     * Generate URL untuk thumbnail
     */
    public function generateThumbnailUrl(string $originalPath, string $sizeName): string
    {
        $thumbnailPath = $this->generateThumbnailPath($originalPath, $sizeName);
        $urlPrefix = $this->getUrlPrefix();

        // Pastikan URL menggunakan forward slash
        $url = rtrim($urlPrefix, '/') . '/' . ltrim($thumbnailPath, '/');
        return str_replace('\\', '/', $url);
    }

    /**
     * Mendapatkan semua URL thumbnail untuk file tertentu
     */
    public function getAllThumbnailUrls(string $originalPath): array
    {
        $urls = [];
        $sizeNames = $this->getThumbnailSizeNames();
        
        foreach ($sizeNames as $sizeName) {
            $urls[$sizeName] = $this->generateThumbnailUrl($originalPath, $sizeName);
        }
        
        return $urls;
    }

    /**
     * Mendapatkan semua path thumbnail untuk file tertentu
     */
    public function getAllThumbnailPaths(string $originalPath): array
    {
        $paths = [];
        $sizeNames = $this->getThumbnailSizeNames();
        
        foreach ($sizeNames as $sizeName) {
            $paths[$sizeName] = $this->generateThumbnailPath($originalPath, $sizeName);
        }
        
        return $paths;
    }

    /**
     * Mendapatkan konfigurasi thumbnail untuk collection tertentu
     */
    public function getCollectionThumbnailConfig(string $collectionName = 'default'): array
    {
        $collectionConfig = $this->getCollectionConfig($collectionName);
        $defaultThumbnailConfig = $this->getThumbnailConfig();
        
        // Merge dengan konfigurasi collection jika ada
        if ($collectionConfig && isset($collectionConfig['thumbnail'])) {
            return array_merge($defaultThumbnailConfig, $collectionConfig['thumbnail']);
        }
        
        return $defaultThumbnailConfig;
    }

    /**
     * Mengecek apakah thumbnail diaktifkan untuk collection tertentu
     */
    public function isThumbnailEnabledForCollection(string $collectionName = 'default'): bool
    {
        $collectionThumbnailConfig = $this->getCollectionThumbnailConfig($collectionName);
        return $collectionThumbnailConfig['enabled'] ?? false;
    }

    /**
     * Mendapatkan ukuran thumbnail untuk collection tertentu
     */
    public function getThumbnailSizesForCollection(string $collectionName = 'default'): array
    {
        $collectionThumbnailConfig = $this->getCollectionThumbnailConfig($collectionName);
        return $collectionThumbnailConfig['sizes'] ?? [];
    }

    /**
     * Mendapatkan URL prefix untuk storage
     */
    public function getUrlPrefix(): string
    {
        $storageConfig = $this->getStorageConfig();
        return $storageConfig['url_prefix'] ?? '/storage';
    }

    /**
     * Mendapatkan disk storage yang digunakan
     */
    public function getStorageDisk(): string
    {
        $storageConfig = $this->getStorageConfig();
        return $storageConfig['disk'] ?? 'public';
    }

    /**
     * Mendapatkan path storage
     */
    public function getStoragePath(): string
    {
        $storageConfig = $this->getStorageConfig();
        return $storageConfig['path'] ?? 'rajapicker';
    }

    /**
     * Mendapatkan TTL cache dalam detik
     */
    public function getCacheTtl(): int
    {
        $performanceConfig = $this->getPerformanceConfig();
        return $performanceConfig['cache_ttl'] ?? 300;
    }

    /**
     * Mengecek apakah cache media list diaktifkan
     */
    public function isCacheEnabled(): bool
    {
        $performanceConfig = $this->getPerformanceConfig();
        return $performanceConfig['cache_media_list'] ?? false;
    }

    /**
     * Mendapatkan maksimal media per halaman
     */
    public function getMaxMediaPerPage(): int
    {
        $performanceConfig = $this->getPerformanceConfig();
        return $performanceConfig['max_media_per_page'] ?? 200;
    }

    /**
     * Mengecek apakah lazy loading diaktifkan
     */
    public function isLazyLoadingEnabled(): bool
    {
        $performanceConfig = $this->getPerformanceConfig();
        return $performanceConfig['lazy_loading'] ?? false;
    }

    public function getFileNamingConfig(): array
    {
        return Config::get('rajapicker.file_naming', [
            'lowercase' => true,
            'separator' => '-',
            'append_timestamp' => true,
            'append_random_length' => 8,
            'pattern' => '{name}{sep}{timestamp}{sep}{random}',
        ]);
    }

    public function getDefaultPreviewThumbnailSize(): string
    {
        $thumbConf = $this->getThumbnailConfig();
        return $thumbConf['default_preview_size'] ?? 'small';
    }
} 