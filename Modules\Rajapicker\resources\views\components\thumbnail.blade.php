@props([
    'imagePath' => null,
    'size' => 'small',
    'alt' => '',
    'class' => '',
    'lazy' => true,
    'fallback' => true
])

@php
    use Modules\Rajapicker\Services\RajaPickerThumbnailService;
    use Modules\Rajapicker\Services\RajaPickerConfigService;
    
    $thumbnailService = new RajaPickerThumbnailService();
    $configService = new RajaPickerConfigService();
    
    $thumbnailUrl = null;
    $originalUrl = null;
    
    if ($imagePath) {
        // Coba dapatkan thumbnail URL
        $thumbnailUrl = $thumbnailService->getThumbnailUrl($imagePath, $size);
        
        // Jika thumbnail tidak ada, gunakan URL asli
        if (!$thumbnailUrl && $fallback) {
            $originalUrl = $configService->getUrlPrefix() . '/' . $imagePath;
        }
    }
    
    $finalUrl = $thumbnailUrl ?: $originalUrl;
@endphp

@if($finalUrl)
    <img 
        src="{{ $finalUrl }}" 
        alt="{{ $alt }}"
        @if($lazy) loading="lazy" @endif
        class="{{ $class }}"
        {{ $attributes }}
    />
@else
    {{-- Fallback image jika tidak ada gambar --}}
    <div class="bg-gray-200 flex items-center justify-center {{ $class }}" {{ $attributes }}>
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
    </div>
@endif 