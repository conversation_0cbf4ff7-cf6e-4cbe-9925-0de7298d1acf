<?php

namespace Modules\Rajapicker\Helpers;

use <PERSON><PERSON><PERSON>\Rajapicker\Services\RajaPickerThumbnailService;
use Mo<PERSON>les\Rajapicker\Services\RajaPickerConfigService;

class RajaPickerThumbnailHelper
{
    protected static RajaPickerThumbnailService $thumbnailService;
    protected static RajaPickerConfigService $configService;

    protected static function getServices()
    {
        if (!isset(self::$thumbnailService)) {
            self::$thumbnailService = new RajaPickerThumbnailService();
        }
        if (!isset(self::$configService)) {
            self::$configService = new RajaPickerConfigService();
        }
    }

    /**
     * Get thumbnail URL
     */
    public static function url(string $imagePath, string $size = 'small'): ?string
    {
        self::getServices();
        return self::$thumbnailService->getThumbnailUrl($imagePath, $size);
    }

    /**
     * Get all thumbnail URLs
     */
    public static function urls(string $imagePath): array
    {
        self::getServices();
        return self::$thumbnailService->getAllThumbnailUrls($imagePath);
    }

    /**
     * Generate thumbnail
     */
    public static function generate(string $imagePath, string $size = 'small'): ?string
    {
        self::getServices();
        return self::$thumbnailService->generateThumbnail($imagePath, $size);
    }

    /**
     * Generate all thumbnails
     */
    public static function generateAll(string $imagePath): array
    {
        self::getServices();
        return self::$thumbnailService->generateAllThumbnails($imagePath);
    }

    /**
     * Check if thumbnail exists
     */
    public static function exists(string $imagePath, string $size = 'small'): bool
    {
        self::getServices();
        return self::$thumbnailService->thumbnailExists($imagePath, $size);
    }

    /**
     * Get thumbnail path
     */
    public static function path(string $imagePath, string $size = 'small'): string
    {
        self::getServices();
        return self::$configService->generateThumbnailPath($imagePath, $size);
    }

    /**
     * Get thumbnail name
     */
    public static function name(string $imagePath, string $size = 'small'): string
    {
        self::getServices();
        return self::$configService->generateThumbnailName(basename($imagePath), $size);
    }

    /**
     * Check if thumbnail is enabled
     */
    public static function isEnabled(): bool
    {
        self::getServices();
        return self::$configService->isThumbnailEnabled();
    }

    /**
     * Get thumbnail sizes
     */
    public static function sizes(): array
    {
        self::getServices();
        return self::$configService->getThumbnailSizes();
    }

    /**
     * Get thumbnail size names
     */
    public static function sizeNames(): array
    {
        self::getServices();
        return self::$configService->getThumbnailSizeNames();
    }

    /**
     * Get thumbnail config
     */
    public static function config(): array
    {
        self::getServices();
        return self::$configService->getThumbnailConfig();
    }

    /**
     * Get responsive image HTML
     */
    public static function responsive(string $imagePath, string $alt = '', array $attributes = []): string
    {
        self::getServices();
        
        $thumbnailUrls = self::$thumbnailService->getAllThumbnailUrls($imagePath);
        $originalUrl = self::$configService->getUrlPrefix() . '/' . $imagePath;
        
        $srcset = [];
        foreach ($thumbnailUrls as $size => $url) {
            $sizeConfig = self::$configService->getThumbnailSizeConfig($size);
            if ($sizeConfig) {
                $srcset[] = "{$url} {$sizeConfig['width']}w";
            }
        }
        
        $srcset[] = "{$originalUrl} 1200w";
        
        $attributes['srcset'] = implode(', ', $srcset);
        $attributes['sizes'] = $attributes['sizes'] ?? '(max-width: 768px) 100vw, 50vw';
        $attributes['alt'] = $alt;
        $attributes['loading'] = $attributes['loading'] ?? 'lazy';
        
        $html = '<img';
        foreach ($attributes as $key => $value) {
            $html .= " {$key}=\"" . htmlspecialchars($value) . "\"";
        }
        $html .= ' />';
        
        return $html;
    }

    /**
     * Get picture element HTML with WebP support
     */
    public static function picture(string $imagePath, string $alt = '', array $attributes = []): string
    {
        self::getServices();
        
        $thumbnailUrls = self::$thumbnailService->getAllThumbnailUrls($imagePath);
        $originalUrl = self::$configService->getUrlPrefix() . '/' . $imagePath;
        
        $html = '<picture>';
        
        // WebP sources
        if (self::$configService->isThumbnailWebpEnabled()) {
            foreach ($thumbnailUrls as $size => $url) {
                $sizeConfig = self::$configService->getThumbnailSizeConfig($size);
                if ($sizeConfig) {
                    $webpUrl = str_replace(['.jpg', '.jpeg', '.png'], '.webp', $url);
                    $html .= "<source srcset=\"{$webpUrl}\" media=\"(max-width: {$sizeConfig['width']}px)\" type=\"image/webp\">";
                }
            }
        }
        
        // Regular image sources
        $srcset = [];
        foreach ($thumbnailUrls as $size => $url) {
            $sizeConfig = self::$configService->getThumbnailSizeConfig($size);
            if ($sizeConfig) {
                $srcset[] = "{$url} {$sizeConfig['width']}w";
            }
        }
        $srcset[] = "{$originalUrl} 1200w";
        
        $attributes['srcset'] = implode(', ', $srcset);
        $attributes['sizes'] = $attributes['sizes'] ?? '(max-width: 768px) 100vw, 50vw';
        $attributes['alt'] = $alt;
        $attributes['loading'] = $attributes['loading'] ?? 'lazy';
        
        $html .= '<img';
        foreach ($attributes as $key => $value) {
            $html .= " {$key}=\"" . htmlspecialchars($value) . "\"";
        }
        $html .= ' />';
        
        $html .= '</picture>';
        
        return $html;
    }

    /**
     * Get background image CSS
     */
    public static function background(string $imagePath, string $size = 'small'): string
    {
        self::getServices();
        $thumbnailUrl = self::$thumbnailService->getThumbnailUrl($imagePath, $size);
        if ($thumbnailUrl) {
            return "background-image: url('{$thumbnailUrl}')";
        }
        
        $originalUrl = self::$configService->getUrlPrefix() . '/' . $imagePath;
        return "background-image: url('{$originalUrl}')";
    }

    /**
     * Get CSS for responsive background
     */
    public static function responsiveBackground(string $imagePath): string
    {
        self::getServices();
        
        $thumbnailUrls = self::$thumbnailService->getAllThumbnailUrls($imagePath);
        $originalUrl = self::$configService->getUrlPrefix() . '/' . $imagePath;
        
        $css = [];
        
        // Mobile first approach
        $smallUrl = isset($thumbnailUrls['small']) ? $thumbnailUrls['small'] : $originalUrl;
        $css[] = "background-image: url('{$smallUrl}')";
        
        if (isset($thumbnailUrls['medium'])) {
            $css[] = "@media (min-width: 768px) { background-image: url('{$thumbnailUrls['medium']}'); }";
        }
        
        if (isset($thumbnailUrls['large'])) {
            $css[] = "@media (min-width: 1024px) { background-image: url('{$thumbnailUrls['large']}'); }";
        }
        
        $css[] = "@media (min-width: 1200px) { background-image: url('{$originalUrl}'); }";
        
        return implode('; ', $css);
    }
} 