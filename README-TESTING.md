# Testing RajaPicker dengan Playwright

Dokumen ini menjelaskan cara menjalankan dan menggunakan test Playwright untuk komponen RajaPicker.

## Instalasi

### 1. Install Dependencies

```bash
npm install
```

### 2. Install Playwright Browsers

```bash
npm run test:install
```

## Menjalankan Test

### Menjalankan Semua Test

```bash
npm test
```

### Menjalankan Test dengan UI Mode

```bash
npm run test:ui
```

### Menjalankan Test dengan Browser Terlihat

```bash
npm run test:headed
```

### Menjalankan Test dalam Mode Debug

```bash
npm run test:debug
```

### Menampilkan Report

```bash
npm run test:report
```

## Struktur Test

### File Test Utama
- `tests/Browser/RajaPicker.spec.ts` - Test utama untuk komponen RajaPicker

### Test Cases yang Dicakup

#### 1. Functional Testing
- ✅ Menampilkan komponen RajaPicker dengan benar
- ✅ Membuka modal galeri
- ✅ Menutup modal galeri
- ✅ Menampilkan loading state
- ✅ Menampilkan grid media
- ✅ Preview gambar
- ✅ Seleksi gambar
- ✅ Konfirmasi seleksi
- ✅ Menampilkan preview area
- ✅ Menghapus gambar yang dipilih
- ✅ Upload file
- ✅ Loading state saat upload
- ✅ Filter berdasarkan collection
- ✅ Pagination
- ✅ Mencegah form submission saat picker terbuka
- ✅ Menampilkan placeholder
- ✅ Seleksi multiple gambar
- ✅ Informasi file
- ✅ Navigasi keyboard
- ✅ Error handling untuk file tidak valid
- ✅ Mempertahankan state setelah refresh

#### 2. Accessibility Testing
- ✅ ARIA labels
- ✅ Keyboard accessibility
- ✅ Focus management

#### 3. Responsive Design Testing
- ✅ Mobile devices
- ✅ Tablet devices

## Konfigurasi

### playwright.config.ts

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:8000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  webServer: {
    command: 'php artisan serve',
    url: 'http://localhost:8000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
});
```

## File Fixtures

### tests/fixtures/
- `test-image.jpg` - File gambar untuk testing upload
- `invalid-file.txt` - File tidak valid untuk testing error handling

## Prasyarat

### 1. Server Laravel Berjalan
Pastikan server Laravel berjalan di `http://localhost:8000`

```bash
php artisan serve
```

### 2. Database Setup
Pastikan database sudah disetup dengan data yang diperlukan untuk testing

### 3. Authentication
Jika halaman memerlukan authentication, pastikan sudah login atau setup test user

## Menambahkan Test Baru

### 1. Test Case Baru

```typescript
test('should handle new feature', async ({ page }) => {
  // Setup
  await page.goto('/admin/cms/create');
  await page.waitForLoadState('networkidle');

  // Action
  const element = page.locator('.selector');
  await element.click();

  // Assertion
  await expect(element).toBeVisible();
});
```

### 2. Test Group Baru

```typescript
test.describe('New Feature Group', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin/cms/create');
    await page.waitForLoadState('networkidle');
  });

  test('should work correctly', async ({ page }) => {
    // Test implementation
  });
});
```

## Best Practices

### 1. Selector Strategy
- Gunakan selector yang stabil dan tidak mudah berubah
- Prioritaskan `data-testid` attributes
- Hindari selector yang bergantung pada text content

### 2. Waiting Strategy
- Gunakan `waitForLoadState('networkidle')` untuk menunggu halaman selesai loading
- Gunakan `waitForSelector()` untuk menunggu elemen muncul
- Hindari `page.waitForTimeout()` kecuali benar-benar diperlukan

### 3. Assertions
- Gunakan assertion yang spesifik
- Test behavior, bukan implementation
- Gunakan `toBeVisible()` daripada `toBeTruthy()`

### 4. Test Data
- Gunakan fixtures untuk test data
- Cleanup data setelah test
- Gunakan unique data untuk setiap test

## Troubleshooting

### 1. Test Gagal karena Selector
- Periksa apakah selector masih valid
- Gunakan Playwright Inspector untuk debug: `npm run test:debug`

### 2. Test Gagal karena Timing
- Tambahkan wait yang sesuai
- Gunakan `waitForSelector()` dengan timeout yang cukup

### 3. Test Gagal karena Authentication
- Setup authentication di `beforeEach`
- Gunakan test user yang konsisten

### 4. Test Gagal karena Data
- Pastikan data test tersedia
- Cleanup data setelah test
- Gunakan database transactions jika memungkinkan

## Continuous Integration

### GitHub Actions Example

```yaml
name: Playwright Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - name: Install dependencies
      run: npm ci
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    - name: Run Playwright tests
      run: npx playwright test
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: |
          playwright-report/
          test-results/
```

## Report dan Analytics

### HTML Report
Setelah menjalankan test, report HTML akan tersedia di:
- `playwright-report/index.html`

### Screenshots dan Videos
- Screenshots: `test-results/`
- Videos: `test-results/`

### Trace Files
- Trace files: `test-results/`
- Buka dengan: `npx playwright show-trace trace.zip`

## Kesimpulan

Test suite ini memberikan coverage yang komprehensif untuk komponen RajaPicker, mencakup:
- Functional testing
- Accessibility testing  
- Responsive design testing
- Error handling
- User interaction flows

Pastikan untuk menjalankan test secara regular dan update test cases ketika ada perubahan pada komponen. 