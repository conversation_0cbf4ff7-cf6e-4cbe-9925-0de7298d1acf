# RajaPicker Thumbnail Guide

## Overview

RajaPicker sekarang mendukung fitur thumbnail generation yang dapat dikonfigurasi secara fleksibel. Fitur ini memungkinkan Anda untuk membuat thumbnail dengan berbagai ukuran secara otomatis saat file diupload.

## Konfigurasi Thumbnail

### 1. Pengaturan Dasar

```php
'storage' => [
    'thumbnail' => [
        'enabled' => true,                    // Aktifkan/nonaktifkan thumbnail generation
        'directory' => 'thumbnails',          // Direktori penyimpanan thumbnail
        'prefix' => '{name}_thumb',           // Prefix nama file thumbnail
        'webp_conversion' => true,            // Konversi ke WebP
        'sizes' => [
            'small' => [
                'width' => 150,
                'height' => 150,
                'quality' => 85,
                'suffix' => 'sm'
            ],
            'medium' => [
                'width' => 300,
                'height' => 300,
                'quality' => 85,
                'suffix' => 'md'
            ],
            'large' => [
                'width' => 600,
                'height' => 600,
                'quality' => 85,
                'suffix' => 'lg'
            ]
        ],
    ],
]
```

### 2. Parameter Konfigurasi

| Parameter | Tipe | Default | Deskripsi |
|-----------|------|---------|-----------|
| `enabled` | boolean | `true` | Aktifkan/nonaktifkan thumbnail generation |
| `directory` | string | `'thumbnails'` | Direktori penyimpanan thumbnail |
| `prefix` | string | `'{name}_thumb'` | Prefix nama file thumbnail |
| `webp_conversion` | boolean | `true` | Konversi thumbnail ke format WebP |
| `sizes` | array | - | Konfigurasi ukuran thumbnail |

### 3. Konfigurasi Ukuran Thumbnail

Setiap ukuran thumbnail memiliki parameter berikut:

| Parameter | Tipe | Deskripsi |
|-----------|------|-----------|
| `width` | integer | Lebar thumbnail dalam pixel |
| `height` | integer | Tinggi thumbnail dalam pixel |
| `quality` | integer | Kualitas kompresi (1-100) |
| `suffix` | string | Suffix untuk nama file |

## Penggunaan Service

### 1. RajaPickerThumbnailService

```php
use Modules\Rajapicker\Services\RajaPickerThumbnailService;

$thumbnailService = new RajaPickerThumbnailService();

// Generate single thumbnail
$thumbnailPath = $thumbnailService->generateThumbnail('path/to/image.jpg', 'small');

// Generate all thumbnails
$thumbnails = $thumbnailService->generateAllThumbnails('path/to/image.jpg');

// Get single thumbnail URL
$url = $thumbnailService->getThumbnailUrl('path/to/image.jpg', 'small');

// Get all thumbnail URLs
$urls = $thumbnailService->getAllThumbnailUrls('path/to/image.jpg');
```

### 2. RajaPickerConfigService

```php
use Modules\Rajapicker\Services\RajaPickerConfigService;

$configService = new RajaPickerConfigService();

// Cek apakah thumbnail diaktifkan
$isEnabled = $configService->isThumbnailEnabled();

// Dapatkan konfigurasi thumbnail
$thumbnailConfig = $configService->getThumbnailConfig();

// Dapatkan ukuran thumbnail
$sizes = $configService->getThumbnailSizes();

// Generate nama thumbnail
$thumbnailName = $configService->generateThumbnailName('image.jpg', 'small');

// Generate path thumbnail
$thumbnailPath = $configService->generateThumbnailPath('path/to/image.jpg', 'small');

// Generate URL thumbnail
$thumbnailUrl = $configService->generateThumbnailUrl('path/to/image.jpg', 'small');
```

## API Endpoints

### 1. Generate Thumbnail

```http
POST /api/rajapicker/thumbnail/generate
Content-Type: application/json

{
    "file_path": "path/to/image.jpg",
    "size": "small"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Thumbnail berhasil dibuat",
    "data": {
        "thumbnail_path": "thumbnails/image_thumb_small.jpg",
        "thumbnail_url": "/storage/thumbnails/image_thumb_small.jpg",
        "size": "small",
        "original_path": "path/to/image.jpg"
    }
}
```

### 2. Regenerate Thumbnail

```http
POST /api/rajapicker/thumbnail/regenerate
Content-Type: application/json

{
    "file_path": "path/to/image.jpg",
    "size": "small"
}
```

### 3. Delete Thumbnails

```http
DELETE /api/rajapicker/thumbnail/delete
Content-Type: application/json

{
    "file_path": "path/to/image.jpg",
    "size": "small"
}
```

### 4. Batch Operations

```http
POST /api/rajapicker/thumbnail/batch
Content-Type: application/json

{
    "action": "generate",
    "file_paths": ["path1.jpg", "path2.jpg"],
    "size": "small"
}
```

### 5. Cleanup Orphaned Thumbnails

```http
POST /api/rajapicker/thumbnail/cleanup
```

### 6. Get Configuration

```http
GET /api/rajapicker/thumbnail/config
```

**Response:**
```json
{
    "success": true,
    "data": {
        "enabled": true,
        "directory": "thumbnails",
        "prefix": "{name}_thumb",
        "webp_conversion": true,
        "sizes": {
            "small": {
                "width": 150,
                "height": 150,
                "quality": 85,
                "suffix": "sm"
            },
            "medium": {
                "width": 300,
                "height": 300,
                "quality": 85,
                "suffix": "md"
            },
            "large": {
                "width": 600,
                "height": 600,
                "quality": 85,
                "suffix": "lg"
            }
        },
        "size_names": ["small", "medium", "large"]
    }
}
```

## Penggunaan dalam Komponen

### 1. RajaPickerWithConfig

```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;

$picker = RajaPickerWithConfig::make('image')
    ->collection('products')
    ->label('Gambar Produk');

// Cek apakah thumbnail diaktifkan
if ($picker->isThumbnailEnabled()) {
    // Get thumbnail URL untuk media tertentu
    $thumbnailUrl = $picker->getMediaThumbnailUrl($mediaId, 'small');
    
    // Get semua thumbnail URL
    $thumbnailUrls = $picker->getMediaAllThumbnailUrls($mediaId);
    
    // Generate thumbnail
    $thumbnailPath = $picker->generateMediaThumbnail($mediaId, 'small');
    
    // Generate semua thumbnail
    $thumbnailPaths = $picker->generateMediaAllThumbnails($mediaId);
}
```

### 2. Dalam Resource Filament

```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerWithConfig;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            RajaPickerWithConfig::make('product_image')
                ->label('Gambar Produk')
                ->collection('products')
                ->afterStateUpdated(function ($state, $component) {
                    // Generate thumbnail setelah upload
                    if ($state && $component->isThumbnailEnabled()) {
                        $component->generateMediaAllThumbnails($state);
                    }
                }),
        ]);
}
```

## Contoh Implementasi

### 1. Auto-generate Thumbnail saat Upload

```php
use Modules\Rajapicker\Services\RajaPickerThumbnailService;

class ProductController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:5120',
        ]);

        // Upload file
        $path = $request->file('image')->store('products', 'public');

        // Generate thumbnails
        $thumbnailService = new RajaPickerThumbnailService();
        $thumbnails = $thumbnailService->generateAllThumbnails($path);

        // Simpan ke database
        $product = Product::create([
            'name' => $request->name,
            'image' => $path,
            'thumbnail_small' => $thumbnails['small'] ?? null,
            'thumbnail_medium' => $thumbnails['medium'] ?? null,
            'thumbnail_large' => $thumbnails['large'] ?? null,
        ]);

        return response()->json([
            'success' => true,
            'data' => $product,
            'thumbnails' => $thumbnails
        ]);
    }
}
```

### 2. Lazy Loading Thumbnail

```php
class Product extends Model
{
    public function getThumbnailUrlAttribute($size = 'small')
    {
        $thumbnailService = new RajaPickerThumbnailService();
        return $thumbnailService->getThumbnailUrl($this->image, $size);
    }

    public function getAllThumbnailUrlsAttribute()
    {
        $thumbnailService = new RajaPickerThumbnailService();
        return $thumbnailService->getAllThumbnailUrls($this->image);
    }
}

// Penggunaan
$product = Product::find(1);
$smallThumbnail = $product->thumbnail_url; // Auto-generate jika belum ada
$allThumbnails = $product->all_thumbnail_urls;
```

### 3. Batch Processing

```php
use Modules\Rajapicker\Services\RajaPickerThumbnailService;

class ThumbnailJob implements ShouldQueue
{
    public function handle()
    {
        $thumbnailService = new RajaPickerThumbnailService();
        
        // Get semua produk tanpa thumbnail
        $products = Product::whereNull('thumbnail_small')->get();
        
        foreach ($products as $product) {
            if ($product->image) {
                $thumbnails = $thumbnailService->generateAllThumbnails($product->image);
                
                $product->update([
                    'thumbnail_small' => $thumbnails['small'] ?? null,
                    'thumbnail_medium' => $thumbnails['medium'] ?? null,
                    'thumbnail_large' => $thumbnails['large'] ?? null,
                ]);
            }
        }
    }
}
```

### 4. Cleanup Job

```php
use Modules\Rajapicker\Services\RajaPickerThumbnailService;

class CleanupThumbnailJob implements ShouldQueue
{
    public function handle()
    {
        $thumbnailService = new RajaPickerThumbnailService();
        $deletedCount = $thumbnailService->cleanupOrphanedThumbnails();
        
        Log::info("Cleaned up {$deletedCount} orphaned thumbnails");
    }
}
```

## Best Practices

### 1. Konfigurasi Ukuran

- **Small (150x150)**: Untuk preview kecil, icon, avatar
- **Medium (300x300)**: Untuk thumbnail galeri, list view
- **Large (600x600)**: Untuk preview detail, modal

### 2. Kualitas Kompresi

- **Small**: 85% - Balance antara kualitas dan ukuran
- **Medium**: 85% - Balance antara kualitas dan ukuran
- **Large**: 85% - Balance antara kualitas dan ukuran

### 3. WebP Conversion

- Aktifkan WebP conversion untuk performa yang lebih baik
- WebP memiliki ukuran file yang lebih kecil dengan kualitas yang sama
- Fallback ke format asli jika browser tidak mendukung WebP

### 4. Storage Management

- Gunakan cleanup job untuk menghapus thumbnail yang tidak terpakai
- Monitor penggunaan storage secara berkala
- Pertimbangkan CDN untuk thumbnail yang sering diakses

### 5. Performance

- Generate thumbnail secara asynchronous untuk file besar
- Cache thumbnail URL untuk mengurangi query database
- Gunakan lazy loading untuk thumbnail yang tidak langsung terlihat

## Troubleshooting

### 1. Thumbnail Tidak Ter-generate

- Cek apakah thumbnail diaktifkan di konfigurasi
- Pastikan file asli ada di storage
- Cek permission direktori thumbnail
- Lihat log error untuk detail masalah

### 2. Thumbnail Tidak Muncul

- Cek apakah path thumbnail benar
- Pastikan file thumbnail ada di storage
- Cek URL prefix di konfigurasi
- Verifikasi permission file

### 3. WebP Thumbnail Tidak Ter-generate

- Pastikan WebP conversion diaktifkan
- Cek apakah GD atau Imagick extension terinstall
- Verifikasi permission direktori

### 4. Performance Lambat

- Gunakan queue untuk batch processing
- Optimasi ukuran thumbnail
- Pertimbangkan CDN
- Monitor penggunaan memory

## Migration

Jika Anda ingin menambahkan kolom thumbnail ke tabel yang sudah ada:

```php
// Migration
Schema::table('products', function (Blueprint $table) {
    $table->string('thumbnail_small')->nullable();
    $table->string('thumbnail_medium')->nullable();
    $table->string('thumbnail_large')->nullable();
});

// Seeder untuk generate thumbnail existing
$products = Product::whereNotNull('image')->get();
$thumbnailService = new RajaPickerThumbnailService();

foreach ($products as $product) {
    $thumbnails = $thumbnailService->generateAllThumbnails($product->image);
    
    $product->update([
        'thumbnail_small' => $thumbnails['small'] ?? null,
        'thumbnail_medium' => $thumbnails['medium'] ?? null,
        'thumbnail_large' => $thumbnails['large'] ?? null,
    ]);
}
```

Dengan fitur thumbnail ini, RajaPicker menjadi lebih powerful dan dapat menangani berbagai kebutuhan optimasi gambar dengan fleksibilitas yang tinggi. 