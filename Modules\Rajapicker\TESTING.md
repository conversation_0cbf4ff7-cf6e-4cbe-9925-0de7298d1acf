# RajaPicker Thumbnail Testing Guide

## Quick Test

### 1. Test Basic Thumbnail Generation
```bash
# Test thumbnail generation dengan file yang ada
php artisan rajapicker:thumbnail test "path/to/your/image.jpg"
```

### 2. Test Configuration
```bash
# Cek konfigurasi thumbnail
php artisan rajapicker:thumbnail config
```

### 3. Test Manual Generation
```bash
# Generate thumbnail manual
php artisan rajapicker:thumbnail generate --file="path/to/image.jpg" --size="small"

# Regenerate thumbnail
php artisan rajapicker:thumbnail regenerate --file="path/to/image.jpg" --size="small"
```

## Debug Commands

### 1. Debug Path Generation
```bash
php artisan tinker
```

```php
// Debug path generation
$configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
$thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();

// Test path generation
$originalPath = 'test-image.jpg';
$sizeName = 'small';

echo "=== PATH DEBUG ===\n";
echo "Original path: " . $originalPath . "\n";
echo "Size name: " . $sizeName . "\n";
echo "Thumbnail directory: " . $configService->getThumbnailDirectory() . "\n";
echo "Thumbnail prefix: " . $configService->getThumbnailPrefix() . "\n";
echo "Generated name: " . $configService->generateThumbnailName(basename($originalPath), $sizeName) . "\n";
echo "Generated path: " . $configService->generateThumbnailPath($originalPath, $sizeName) . "\n";
echo "Generated URL: " . $configService->generateThumbnailUrl($originalPath, $sizeName) . "\n";
```

### 2. Debug Storage
```php
// Debug storage
$disk = \Storage::disk('public');
$path = 'test-image.jpg';
$thumbnailPath = 'thumbnails/test-image_thumb_sm.jpg';

echo "=== STORAGE DEBUG ===\n";
echo "Storage disk: " . $configService->getStorageDisk() . "\n";
echo "Storage path: " . $configService->getStoragePath() . "\n";
echo "Original exists: " . ($disk->exists($path) ? 'Yes' : 'No') . "\n";
echo "Thumbnail exists: " . ($disk->exists($thumbnailPath) ? 'Yes' : 'No') . "\n";

// List files in thumbnail directory
$thumbnailDir = $configService->getThumbnailDirectory();
echo "Files in {$thumbnailDir}:\n";
$files = $disk->files($thumbnailDir);
foreach ($files as $file) {
    echo "  - " . $file . "\n";
}
```

### 3. Debug Configuration
```php
// Debug configuration
echo "=== CONFIG DEBUG ===\n";
echo "Thumbnail enabled: " . ($configService->isThumbnailEnabled() ? 'Yes' : 'No') . "\n";
echo "WebP enabled: " . ($configService->isThumbnailWebpEnabled() ? 'Yes' : 'No') . "\n";
echo "Available sizes: " . implode(', ', $configService->getThumbnailSizeNames()) . "\n";

$sizes = $configService->getThumbnailSizes();
foreach ($sizes as $sizeName => $sizeConfig) {
    echo "Size {$sizeName}: " . json_encode($sizeConfig) . "\n";
}
```

### 4. Debug Thumbnail Service
```php
// Debug thumbnail service
echo "=== SERVICE DEBUG ===\n";

// Test thumbnail generation
$result = $thumbnailService->generateThumbnail($path, 'small');
echo "Generate result: " . ($result ?: 'null') . "\n";

// Test thumbnail existence
$exists = $thumbnailService->thumbnailExists($path, 'small');
echo "Thumbnail exists: " . ($exists ? 'Yes' : 'No') . "\n";

// Test thumbnail URL
$url = $thumbnailService->getThumbnailUrl($path, 'small');
echo "Thumbnail URL: " . ($url ?: 'null') . "\n";

// Test all thumbnails
$urls = $thumbnailService->getAllThumbnailUrls($path);
echo "All thumbnail URLs: " . json_encode($urls) . "\n";
```

## Common Issues & Solutions

### 1. Thumbnail tidak dibuat
**Gejala:** `generateThumbnail()` return null
**Debug:**
```php
// Cek apakah file asli ada
$disk = \Storage::disk('public');
$path = 'test-image.jpg';
echo "File exists: " . ($disk->exists($path) ? 'Yes' : 'No') . "\n";

// Cek konfigurasi
$configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
echo "Thumbnail enabled: " . ($configService->isThumbnailEnabled() ? 'Yes' : 'No') . "\n";

// Cek error log
echo "Check log: tail -f storage/logs/laravel.log\n";
```

**Solusi:**
- Pastikan file asli ada di storage
- Pastikan thumbnail diaktifkan di konfigurasi
- Cek permission direktori storage
- Cek log error untuk detail

### 2. Path thumbnail salah
**Gejala:** Thumbnail disimpan di lokasi yang berbeda
**Debug:**
```php
$configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
$originalPath = 'test-image.jpg';
$sizeName = 'small';

echo "Expected path: " . $configService->generateThumbnailPath($originalPath, $sizeName) . "\n";
echo "Actual files:\n";
$disk = \Storage::disk('public');
$files = $disk->allFiles('thumbnails');
foreach ($files as $file) {
    echo "  - " . $file . "\n";
}
```

**Solusi:**
- Periksa konfigurasi `thumbnail.directory`
- Periksa konfigurasi `thumbnail.prefix`
- Pastikan path menggunakan forward slash

### 3. Format file salah
**Gejala:** Thumbnail memiliki format yang berbeda
**Debug:**
```php
$configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
$originalPath = 'test-image.jpg';
$sizeName = 'small';

echo "Original extension: " . pathinfo($originalPath, PATHINFO_EXTENSION) . "\n";
echo "Generated name: " . $configService->generateThumbnailName(basename($originalPath), $sizeName) . "\n";
```

**Solusi:**
- Periksa konfigurasi format di `createThumbnail()`
- Pastikan ekstensi file valid
- Regenerate thumbnail dengan format yang benar

### 4. WebP tidak dibuat
**Gejala:** WebP thumbnail tidak ada
**Debug:**
```php
// Cek WebP support
echo "GD WebP: " . (function_exists('imagewebp') ? 'Yes' : 'No') . "\n";
echo "Imagick WebP: " . (extension_loaded('imagick') ? 'Yes' : 'No') . "\n";

// Cek konfigurasi WebP
$configService = new \Modules\Rajapicker\Services\RajaPickerConfigService();
echo "WebP enabled: " . ($configService->isThumbnailWebpEnabled() ? 'Yes' : 'No') . "\n";
```

**Solusi:**
- Install GD dengan WebP support atau Imagick
- Aktifkan WebP di konfigurasi
- Regenerate thumbnail

### 5. Permission error
**Gejala:** Error permission saat membuat thumbnail
**Debug:**
```bash
# Cek permission
ls -la storage/app/public/
ls -la storage/app/public/thumbnails/

# Cek disk space
df -h storage/app/public/
```

**Solusi:**
```bash
# Set permission yang benar
chmod -R 755 storage/app/public/thumbnails
chown -R www-data:www-data storage/app/public/thumbnails

# Buat direktori jika tidak ada
mkdir -p storage/app/public/thumbnails
```

## Performance Testing

### 1. Batch Generation Test
```php
// Test batch generation
$files = [
    'test1.jpg',
    'test2.jpg',
    'test3.jpg'
];

$start = microtime(true);
$results = $thumbnailService->batchGenerateThumbnails($files, 'small');
$end = microtime(true);

echo "Batch generation time: " . ($end - $start) . " seconds\n";
echo "Results: " . json_encode($results) . "\n";
```

### 2. Memory Usage Test
```php
// Test memory usage
$memoryBefore = memory_get_usage();
$thumbnailService->generateAllThumbnails('large-image.jpg');
$memoryAfter = memory_get_usage();

echo "Memory usage: " . number_format(($memoryAfter - $memoryBefore) / 1024 / 1024, 2) . " MB\n";
```

## Integration Testing

### 1. Test with RajaPicker Component
```php
// Test dengan RajaPicker component
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

$picker = RajaPicker::make('image')
    ->collection('test')
    ->directory('test');

// Simulate file upload
$picker->state('test-image.jpg');

// Check if thumbnails are generated
$thumbnails = $thumbnailService->getAllThumbnailUrls('test-image.jpg');
echo "Generated thumbnails: " . json_encode($thumbnails) . "\n";
```

### 2. Test with Model
```php
// Test dengan model
class TestProduct extends \Illuminate\Database\Eloquent\Model
{
    protected $fillable = ['name', 'image'];
}

$product = new TestProduct();
$product->name = 'Test Product';
$product->image = 'test-image.jpg';

// Simulate model save
$thumbnailService->generateAllThumbnails($product->image);

// Check thumbnails
$thumbnails = $thumbnailService->getAllThumbnailUrls($product->image);
echo "Product thumbnails: " . json_encode($thumbnails) . "\n";
```

## Cleanup Testing

### 1. Test Cleanup
```php
// Test cleanup orphaned thumbnails
$deletedCount = $thumbnailService->cleanupOrphanedThumbnails();
echo "Deleted orphaned thumbnails: {$deletedCount}\n";
```

### 2. Test Delete
```php
// Test delete thumbnails
$deleted = $thumbnailService->deleteThumbnails('test-image.jpg');
echo "Deleted thumbnails: " . ($deleted ? 'Yes' : 'No') . "\n";
```

## API Testing

### 1. Test API Endpoints
```bash
# Test generate thumbnail
curl -X POST http://localhost/api/rajapicker/thumbnail/generate \
  -H "Content-Type: application/json" \
  -d '{"file_path": "test-image.jpg", "size": "small"}'

# Test get thumbnail URL
curl -X GET "http://localhost/api/rajapicker/thumbnail/url?file_path=test-image.jpg&size=small"

# Test cleanup
curl -X POST http://localhost/api/rajapicker/thumbnail/cleanup
```

### 2. Test with JavaScript
```javascript
// Test thumbnail generation
fetch('/api/rajapicker/thumbnail/generate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        file_path: 'test-image.jpg',
        size: 'small'
    })
})
.then(response => response.json())
.then(data => {
    console.log('Thumbnail generated:', data);
})
.catch(error => {
    console.error('Error:', error);
});
```

## Log Analysis

### 1. Monitor Logs
```bash
# Monitor thumbnail logs
tail -f storage/logs/laravel.log | grep -i "thumbnail"

# Monitor RajaPicker logs
tail -f storage/logs/laravel.log | grep -i "rajapicker"
```

### 2. Search Logs
```bash
# Search for thumbnail errors
grep -i "thumbnail.*error" storage/logs/laravel.log

# Search for RajaPicker errors
grep -i "rajapicker.*error" storage/logs/laravel.log
```

## Final Test Checklist

- [ ] Thumbnail generation works
- [ ] Path generation is correct
- [ ] File format is preserved
- [ ] WebP conversion works (if enabled)
- [ ] URLs are accessible
- [ ] Cleanup works
- [ ] API endpoints work
- [ ] Performance is acceptable
- [ ] Error handling works
- [ ] Logs are informative 