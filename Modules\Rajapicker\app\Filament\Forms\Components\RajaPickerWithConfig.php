<?php

namespace Modules\Rajapicker\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Modules\Rajapicker\Models\Media;
use Modules\Rajapicker\Services\RajaPickerConfigService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class RajaPickerWithConfig extends Field
{
    protected string $view = 'rajapicker::components.forms.raja-picker';
    
    protected RajaPickerConfigService $configService;
    protected string $collectionName = 'default';
    protected string $themeName = 'default';

    public function __construct(string $name)
    {
        parent::__construct($name);
        $this->configService = new RajaPickerConfigService();
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Load konfigurasi dari config file
        $this->loadConfiguration();

        // Setup dehydration untuk memastikan data tersimpan dengan benar
        $this->dehydrateStateUsing(function ($state) {
            // Jika state adalah string JSON, decode dulu
            if (is_string($state) && $this->isJson($state)) {
                $state = json_decode($state, true);
            }

            // Konversi ID ke URL jika diperlukan
            $state = $this->convertIdsToUrls($state);

            // Clean URLs for storage (remove /storage/ prefix)
            $state = $this->cleanUrlsForStorage($state);

            // Untuk multiple selection, pastikan return array
            if ($this->isMultiple()) {
                return is_array($state) ? $state : ($state ? [$state] : []);
            }

            // Untuk single selection, pastikan return single value
            return is_array($state) ? (count($state) > 0 ? $state[0] : null) : $state;
        });

        // Setup hydration untuk memastikan data dimuat dengan benar
        $this->afterStateHydrated(function ($component, $state) {
            // Konversi URL ke ID untuk kompatibilitas backward jika diperlukan
            $state = $this->convertUrlsToIds($state);

            // Pastikan state dalam format yang benar
            if ($this->isMultiple() && !is_array($state)) {
                $component->state($state ? [$state] : []);
            } elseif (!$this->isMultiple() && is_array($state)) {
                $component->state(count($state) > 0 ? $state[0] : null);
            }
        });

        // Make field reactive untuk memastikan perubahan state terdeteksi
        $this->live();
    }

    /**
     * Load konfigurasi dari config file
     */
    protected function loadConfiguration(): void
    {
        $fieldConfig = $this->configService->getFieldConfig($this->collectionName);
        $themeConfig = $this->configService->getThemeConfig($this->themeName);

        // Set properties berdasarkan konfigurasi
        $this->acceptedFileTypes($fieldConfig['accepted_file_types'] ?? []);
        $this->maxFileSize($fieldConfig['max_file_size'] ?? 10);
        $this->collection($fieldConfig['collection'] ?? 'default');
        $this->multiple($fieldConfig['multiple'] ?? false);
        $this->directory($fieldConfig['directory']);
        $this->enablePicker($fieldConfig['features']['picker'] ?? true);
        $this->enableUploader($fieldConfig['features']['uploader'] ?? true);
        $this->placeholder($fieldConfig['ui']['placeholder'] ?? 'Pilih atau upload gambar...');
        $this->previewSize($fieldConfig['ui']['preview_size'] ?? 150);
        $this->showFileName($fieldConfig['ui']['show_file_name'] ?? true);
        $this->showFileSize($fieldConfig['ui']['show_file_size'] ?? true);
        $this->perPage($fieldConfig['per_page'] ?? 12);
        $this->byUser($fieldConfig['user_filter']['by_user'] ?? false);
        $this->byUserId($fieldConfig['user_filter']['by_user_id']);
        $this->convertWebp($fieldConfig['convert_webp'] ?? false);
    }

    /**
     * Set collection dan load konfigurasinya
     */
    public function collection(string $collection): static
    {
        $this->collectionName = $collection;
        
        // Reload konfigurasi jika collection berubah
        if ($this->isConfigured()) {
            $this->loadConfiguration();
        }
        
        return parent::collection($collection);
    }

    /**
     * Set theme dan load konfigurasinya
     */
    public function theme(string $theme): static
    {
        $this->themeName = $theme;
        
        // Reload konfigurasi jika theme berubah
        if ($this->isConfigured()) {
            $this->loadConfiguration();
        }
        
        return $this;
    }

    /**
     * Set accepted file types dari config
     */
    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }

    /**
     * Set maximum file size dari config
     */
    public function maxFileSize(int $size): static
    {
        $this->maxFileSize = $size;
        return $this;
    }

    /**
     * Set multiple selection dari config
     */
    public function multiple(bool $multiple = true): static
    {
        $this->multiple = $multiple;
        return $this;
    }

    /**
     * Set upload directory dari config
     */
    public function directory(?string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }

    /**
     * Enable/disable picker functionality dari config
     */
    public function enablePicker(bool $enable = true): static
    {
        $this->enablePicker = $enable;
        return $this;
    }

    /**
     * Enable/disable uploader functionality dari config
     */
    public function enableUploader(bool $enable = true): static
    {
        $this->enableUploader = $enable;
        return $this;
    }

    /**
     * Set placeholder text dari config
     */
    public function placeholder(string $placeholder): static
    {
        $this->placeholder = $placeholder;
        return $this;
    }

    /**
     * Set preview image size dari config
     */
    public function previewSize(int $size): static
    {
        $this->previewSize = $size;
        return $this;
    }

    /**
     * Show/hide file name dari config
     */
    public function showFileName(bool $show = true): static
    {
        $this->showFileName = $show;
        return $this;
    }

    /**
     * Show/hide file size dari config
     */
    public function showFileSize(bool $show = true): static
    {
        $this->showFileSize = $show;
        return $this;
    }

    /**
     * Set per page dari config
     */
    public function perPage(int $perPage): static
    {
        $this->perPage = $perPage;
        return $this;
    }

    /**
     * Filter media by current user dari config
     */
    public function byUser(bool $byUser = true): static
    {
        $this->byUser = $byUser;
        return $this;
    }

    /**
     * Filter media by specific user ID dari config
     */
    public function byUserId(?int $userId = null): static
    {
        $this->byUserId = $userId ?? Auth::id();
        return $this;
    }

    /**
     * Enable/disable WebP conversion dari config
     */
    public function convertWebp(bool $convert = false): static
    {
        $this->convertWebp = $convert;
        return $this;
    }

    // Getter methods untuk view
    public function getAcceptedFileTypes(): array
    {
        return $this->configService->getAcceptedFileTypes($this->collectionName);
    }

    public function getAcceptedFileTypesString(): string
    {
        return $this->configService->getAcceptedFileTypesString($this->collectionName);
    }

    public function getMaxFileSize(): int
    {
        return $this->configService->getMaxFileSize($this->collectionName);
    }

    public function getMaxFileSizeBytes(): int
    {
        return $this->configService->getMaxFileSizeBytes($this->collectionName);
    }

    public function getCollection(): string
    {
        return $this->collectionName;
    }

    public function isMultiple(): bool
    {
        return $this->multiple;
    }

    public function getDirectory(): ?string
    {
        return $this->configService->getDirectory($this->collectionName);
    }

    public function isPickerEnabled(): bool
    {
        return $this->enablePicker;
    }

    public function isUploaderEnabled(): bool
    {
        return $this->enableUploader;
    }

    public function getPlaceholder(): string
    {
        return $this->placeholder;
    }

    public function getPreviewSize(): int
    {
        return $this->previewSize;
    }

    public function shouldShowFileName(): bool
    {
        return $this->showFileName;
    }

    public function shouldShowFileSize(): bool
    {
        return $this->showFileSize;
    }

    public function getPerPage(): int
    {
        return $this->perPage;
    }

    public function isByUser(): bool
    {
        return $this->byUser;
    }

    public function getByUserId(): ?int
    {
        return $this->byUserId;
    }

    public function shouldConvertWebp(): bool
    {
        return $this->convertWebp;
    }

    /**
     * Get available media for picker dengan cache jika diaktifkan
     */
    public function getAvailableMedia(): Collection
    {
        $cacheKey = "rajapicker_media_{$this->collectionName}";
        
        if ($this->configService->isCacheEnabled()) {
            return cache()->remember($cacheKey, $this->configService->getCacheTtl(), function () {
                return $this->queryAvailableMedia();
            });
        }
        
        return $this->queryAvailableMedia();
    }

    /**
     * Query untuk mendapatkan media yang tersedia
     */
    protected function queryAvailableMedia(): Collection
    {
        $query = Media::where('collection_name', $this->collectionName)
            ->where('mime_type', 'LIKE', 'image/%');

        // Filter by specific user ID if byUserId is set
        if ($this->byUserId !== null) {
            $query->where('user_id', $this->byUserId);
        }
        // Filter by current user if byUser is enabled and byUserId is not set
        elseif ($this->byUser && Auth::check()) {
            $query->where('user_id', Auth::id());
        }

        return $query->orderBy('created_at', 'desc')
            ->limit($this->configService->getMaxMediaPerPage())
            ->get();
    }

    /**
     * Get all available media from all collections (excluding conversion directory)
     */
    public function getAllAvailableMedia(): Collection
    {
        $query = Media::where('mime_type', 'LIKE', 'image/%')
            ->where(function ($query) {
                // Exclude conversion directory files
                $query->where('collection_name', '!=', 'conversion')
                      ->whereNotLike('file_name', '%/conversion/%');
            });

        // Filter by specific user ID if byUserId is set
        if ($this->byUserId !== null) {
            $query->where('user_id', $this->byUserId);
        }
        // Filter by current user if byUser is enabled and byUserId is not set
        elseif ($this->byUser && Auth::check()) {
            $query->where('user_id', Auth::id());
        }

        return $query->orderBy('created_at', 'desc')
            ->limit($this->configService->getMaxMediaPerPage())
            ->get();
    }

    /**
     * Get media by ID
     */
    public function getMediaById($id): ?Media
    {
        if (empty($id)) {
            return null;
        }

        return Media::find($id);
    }

    /**
     * Get media by IDs (for multiple)
     */
    public function getMediaByIds(array $ids): Collection
    {
        if (empty($ids)) {
            return collect();
        }

        return Media::whereIn('id', $ids)->get();
    }

    /**
     * Check if string is valid JSON
     */
    private function isJson($string): bool
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Remove /storage/ prefix from URL for database storage
     */
    private function removeStoragePrefix(string $url): string
    {
        if (str_starts_with($url, '/storage/')) {
            $cleanUrl = substr($url, 8); // Remove '/storage' (8 characters)
            // Remove leading slash for cleaner storage
            if (str_starts_with($cleanUrl, '/')) {
                $cleanUrl = substr($cleanUrl, 1);
            }
            return $cleanUrl;
        }
        return $url;
    }

    /**
     * Add /storage/ prefix to URL for display purposes
     */
    private function addStoragePrefix(string $url): string
    {
        // Don't add prefix if URL already has it or is a full URL
        if (str_starts_with($url, '/storage/') || str_starts_with($url, 'http')) {
            return $url;
        }

        // Add /storage/ prefix - handle both with and without leading slash
        if (str_starts_with($url, '/')) {
            return '/storage' . $url;
        }

        return '/storage/' . $url;
    }

    /**
     * Clean URL for storage (remove /storage/ prefix and leading slash)
     */
    private function cleanUrlForStorage($url): ?string
    {
        if (empty($url) || !is_string($url)) {
            return $url;
        }

        // Remove /storage/ prefix if present
        $cleanUrl = $this->removeStoragePrefix($url);

        // Remove leading slash for cleaner storage
        if (str_starts_with($cleanUrl, '/') && !str_starts_with($cleanUrl, 'http')) {
            $cleanUrl = substr($cleanUrl, 1);
        }

        return $cleanUrl;
    }

    /**
     * Clean URLs for storage (handle both single URL and array of URLs)
     */
    private function cleanUrlsForStorage($state)
    {
        if (empty($state)) {
            return $state;
        }

        if (is_array($state)) {
            return array_map(function($url) {
                return $this->cleanUrlForStorage($url);
            }, $state);
        }

        return $this->cleanUrlForStorage($state);
    }

    /**
     * Convert IDs to URLs (without /storage/ prefix for storage)
     */
    private function convertIdsToUrls($state)
    {
        if (empty($state)) {
            return $state;
        }

        // Jika sudah berupa URL, clean it for storage
        if (is_string($state) && (str_starts_with($state, 'http') || str_starts_with($state, '/'))) {
            return $this->cleanUrlForStorage($state);
        }

        if (is_array($state)) {
            $urls = [];
            foreach ($state as $item) {
                if (is_numeric($item)) {
                    $media = Media::find($item);
                    if ($media) {
                        // Use relative URL (without /storage/ prefix) for storage
                        $urls[] = $media->relative_url ?? $this->cleanUrlForStorage($media->url);
                    }
                } elseif (is_string($item) && (str_starts_with($item, 'http') || str_starts_with($item, '/'))) {
                    $urls[] = $this->cleanUrlForStorage($item);
                }
            }
            return $urls;
        } elseif (is_numeric($state)) {
            $media = Media::find($state);
            return $media ? ($media->relative_url ?? $this->cleanUrlForStorage($media->url)) : null;
        }

        return $state;
    }

    /**
     * Convert URLs to IDs for backward compatibility
     */
    private function convertUrlsToIds($state)
    {
        if (empty($state)) {
            return $state;
        }

        // Jika sudah berupa ID numeric, return as is
        if (is_numeric($state)) {
            return $state;
        }

        if (is_array($state)) {
            $ids = [];
            foreach ($state as $item) {
                if (is_string($item) && (str_starts_with($item, 'http') || str_starts_with($item, '/'))) {
                    // Normalize URL (add /storage/ prefix if needed for matching)
                    $normalizedUrl = $this->addStoragePrefix($item);

                    // Extract filename dari URL untuk mencari media
                    $filename = basename(parse_url($normalizedUrl, PHP_URL_PATH));
                    $media = Media::where('file_name', $filename)
                        ->where('collection_name', $this->collectionName)
                        ->first();
                    if ($media) {
                        $ids[] = $media->id;
                    }
                } elseif (is_numeric($item)) {
                    $ids[] = $item;
                }
            }
            return $ids;
        } elseif (is_string($state) && (str_starts_with($state, 'http') || str_starts_with($state, '/'))) {
            // Normalize URL (add /storage/ prefix if needed for matching)
            $normalizedUrl = $this->addStoragePrefix($state);

            // Extract filename dari URL untuk mencari media
            $filename = basename(parse_url($normalizedUrl, PHP_URL_PATH));
            $media = Media::where('file_name', $filename)
                ->where('collection_name', $this->collectionName)
                ->first();
            return $media ? $media->id : null;
        }

        return $state;
    }

    /**
     * Get theme configuration
     */
    public function getThemeConfig(): array
    {
        return $this->configService->getThemeConfig($this->themeName) ?? [];
    }

    /**
     * Get performance configuration
     */
    public function getPerformanceConfig(): array
    {
        return $this->configService->getPerformanceConfig();
    }

    /**
     * Get security configuration
     */
    public function getSecurityConfig(): array
    {
        return $this->configService->getSecurityConfig();
    }

    /**
     * Get thumbnail URL untuk media tertentu
     */
    public function getMediaThumbnailUrl($mediaId, string $sizeName = 'small'): ?string
    {
        $media = $this->getMediaById($mediaId);
        if (!$media) {
            return null;
        }

        $thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();
        return $thumbnailService->getThumbnailUrl($media->file_name, $sizeName);
    }

    /**
     * Get semua thumbnail URL untuk media tertentu
     */
    public function getMediaAllThumbnailUrls($mediaId): array
    {
        $media = $this->getMediaById($mediaId);
        if (!$media) {
            return [];
        }

        $thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();
        return $thumbnailService->getAllThumbnailUrls($media->file_name);
    }

    /**
     * Generate thumbnail untuk media tertentu
     */
    public function generateMediaThumbnail($mediaId, string $sizeName = 'small'): ?string
    {
        $media = $this->getMediaById($mediaId);
        if (!$media) {
            return null;
        }

        $thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();
        return $thumbnailService->generateThumbnail($media->file_name, $sizeName);
    }

    /**
     * Generate semua thumbnail untuk media tertentu
     */
    public function generateMediaAllThumbnails($mediaId): array
    {
        $media = $this->getMediaById($mediaId);
        if (!$media) {
            return [];
        }

        $thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();
        return $thumbnailService->generateAllThumbnails($media->file_name);
    }

    /**
     * Cek apakah thumbnail diaktifkan untuk collection ini
     */
    public function isThumbnailEnabled(): bool
    {
        return $this->configService->isThumbnailEnabledForCollection($this->collectionName);
    }

    /**
     * Get thumbnail sizes untuk collection ini
     */
    public function getThumbnailSizes(): array
    {
        return $this->configService->getThumbnailSizesForCollection($this->collectionName);
    }

    /**
     * Get thumbnail configuration untuk collection ini
     */
    public function getThumbnailConfig(): array
    {
        return $this->configService->getCollectionThumbnailConfig($this->collectionName);
    }
} 