<?php

namespace Modules\Rajapicker\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Rajapicker\Models\Media;
use Modules\Rajapicker\Models\RajaGaleri;
use Mo<PERSON>les\Rajapicker\Services\RajaPickerThumbnailService;

class ImageEditorController extends Controller
{
    protected RajaPickerThumbnailService $thumbnailService;

    public function __construct(RajaPickerThumbnailService $thumbnailService)
    {
        $this->thumbnailService = $thumbnailService;
    }

    /**
     * Get media by ID for editing
     */
    public function getMedia(int $id): JsonResponse
    {
        try {
            $media = Media::find($id);
            
            if (!$media || !$media->isImage()) {
                return response()->json(['error' => 'Media tidak ditemukan atau bukan gambar'], 404);
            }

            $result = [
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'url' => $this->getRelativeUrl($media),
                'full_url' => $this->getFullUrl($media),
                'mime_type' => $media->mime_type,
                'size' => $media->size,
                'collection_name' => $media->collection_name,
                'dimensions' => $this->getImageDimensions($media),
                'created_at' => $media->created_at,
                'file_exists' => file_exists($media->getPath()),
            ];

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting media for editor: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Save edited image
     */
    public function saveEditedImage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'media_id' => 'required|integer|exists:media,id',
                'image_data' => 'required|string',
                'save_as_new' => 'boolean',
                'name' => 'nullable|string|max:255',
            ]);

            $originalMedia = Media::find($request->media_id);
            if (!$originalMedia || !$originalMedia->isImage()) {
                return response()->json(['error' => 'Media tidak ditemukan'], 404);
            }

            // Decode base64 image data
            $imageData = $request->image_data;

            // Validate image data format
            if (!$imageData || !is_string($imageData)) {
                return response()->json(['error' => 'Data gambar tidak valid'], 400);
            }

            // Extract base64 content
            if (strpos($imageData, 'data:image') === 0) {
                // Remove data URL prefix
                $base64Content = preg_replace('#^data:image/[^;]+;base64,#', '', $imageData);
            } else {
                $base64Content = $imageData;
            }

            // Decode base64
            $imageContent = base64_decode($base64Content, true);

            if ($imageContent === false || empty($imageContent)) {
                return response()->json(['error' => 'Gagal decode data gambar'], 400);
            }

            // Validate image content (check if it's a valid image)
            $imageInfo = @getimagesizefromstring($imageContent);
            if ($imageInfo === false) {
                return response()->json(['error' => 'Data bukan gambar yang valid'], 400);
            }

            Log::info('Image data decoded successfully', [
                'size' => strlen($imageContent),
                'dimensions' => $imageInfo[0] . 'x' . $imageInfo[1],
                'mime' => $imageInfo['mime']
            ]);

            $saveAsNew = $request->boolean('save_as_new', true);
            $customName = $request->name;

            if ($saveAsNew) {
                // Save as new file
                $newMedia = $this->saveAsNewFile($originalMedia, $imageContent, $customName);
                $result = [
                    'id' => $newMedia->id,
                    'name' => $newMedia->name,
                    'file_name' => $newMedia->file_name,
                    'url' => $this->getRelativeUrl($newMedia),
                    'full_url' => $this->getFullUrl($newMedia),
                    'collection_name' => $newMedia->collection_name,
                    'message' => 'Gambar berhasil disimpan sebagai file baru'
                ];
            } else {
                // Replace existing file
                $this->replaceExistingFile($originalMedia, $imageContent, $customName);
                $result = [
                    'id' => $originalMedia->id,
                    'name' => $originalMedia->name,
                    'file_name' => $originalMedia->file_name,
                    'url' => $this->getRelativeUrl($originalMedia),
                    'full_url' => $this->getFullUrl($originalMedia),
                    'collection_name' => $originalMedia->collection_name,
                    'message' => 'Gambar berhasil diperbarui'
                ];
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error saving edited image: ' . $e->getMessage());
            return response()->json(['error' => 'Gagal menyimpan gambar: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get all available media for selection
     */
    public function getAvailableMedia(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 24);
            $collection = $request->get('collection', null);

            $query = Media::where('mime_type', 'LIKE', 'image/%')
                ->where('collection_name', '!=', 'conversion')
                ->whereNotLike('file_name', '%/conversion/%');

            if ($collection && $collection !== 'all') {
                $query->where('collection_name', $collection);
            }

            $media = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            $result = $media->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'file_name' => $item->file_name,
                    'url' => $this->getRelativeUrl($item),
                    'mime_type' => $item->mime_type,
                    'size' => $item->size,
                    'collection_name' => $item->collection_name,
                    'dimensions' => $this->getImageDimensions($item),
                    'created_at' => $item->created_at,
                ];
            });

            return response()->json([
                'data' => $result,
                'pagination' => [
                    'current_page' => $media->currentPage(),
                    'last_page' => $media->lastPage(),
                    'per_page' => $media->perPage(),
                    'total' => $media->total(),
                    'from' => $media->firstItem(),
                    'to' => $media->lastItem(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting available media: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan'], 500);
        }
    }

    /**
     * Save image as new file
     */
    private function saveAsNewFile($originalMedia, $imageContent, $customName = null): Media
    {
        // Generate filename
        $pathInfo = pathinfo($originalMedia->file_name);
        $newFileName = $pathInfo['filename'] . '_edited_' . time() . '.' . $pathInfo['extension'];

        // Create new RajaGaleri instance
        $rajaGaleri = new RajaGaleri();
        $rajaGaleri->save();

        // Create temporary file with unique name to avoid conflicts
        $tempDir = storage_path('app/temp');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $tempFileName = 'edited_' . uniqid() . '_' . time() . '.png';
        $tempPath = $tempDir . '/' . $tempFileName;

        try {
            // Write image content to temp file
            $bytesWritten = file_put_contents($tempPath, $imageContent);
            if ($bytesWritten === false) {
                throw new \Exception('Failed to write temporary file');
            }

            // Verify file exists and has content
            if (!file_exists($tempPath) || filesize($tempPath) === 0) {
                throw new \Exception('Temporary file is empty or does not exist');
            }

            // Determine name
            $name = $customName ?: ($originalMedia->name . ' (Edited)');

            // Add media to collection
            $media = $rajaGaleri
                ->addMedia($tempPath)
                ->usingName($name)
                ->usingFileName($newFileName)
                ->toMediaCollection($originalMedia->collection_name);

            // Verify the saved file
            $savedPath = $media->getPath();
            if (file_exists($savedPath)) {
                $savedSize = filesize($savedPath);
                Log::info('Media saved successfully', [
                    'id' => $media->id,
                    'path' => $savedPath,
                    'size' => $savedSize,
                    'url' => $this->getRelativeUrl($media)
                ]);

                // Generate thumbnail menggunakan service custom
                $this->generateThumbnailsForMedia($media);
            } else {
                Log::error('Saved media file not found: ' . $savedPath);
            }

            return $media;

        } catch (\Exception $e) {
            Log::error('Error in saveAsNewFile: ' . $e->getMessage());
            throw $e;
        } finally {
            // Clean up temp file if it exists
            if (file_exists($tempPath)) {
                try {
                    unlink($tempPath);
                } catch (\Exception $e) {
                    Log::warning('Could not delete temp file: ' . $tempPath . ' - ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Replace existing file
     */
    private function replaceExistingFile($media, $imageContent, $customName = null): void
    {
        try {
            // Get file path
            $prefix = config('media-library.prefix', 'uploads');
            $filePath = storage_path('app/public/' . $prefix . '/' . $media->collection_name . '/' . $media->file_name);

            // Ensure directory exists
            $directory = dirname($filePath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Backup original file if it exists
            if (file_exists($filePath)) {
                $backupPath = $filePath . '.backup.' . time();
                copy($filePath, $backupPath);
            }

            // Write new content
            $bytesWritten = file_put_contents($filePath, $imageContent);
            if ($bytesWritten === false) {
                throw new \Exception('Failed to write file: ' . $filePath);
            }

            // Update name if provided
            if ($customName) {
                $media->update(['name' => $customName]);
            }

            Log::info('File replaced successfully: ' . $filePath);
        } catch (\Exception $e) {
            Log::error('Error replacing file: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate relative URL without domain for media
     */
    private function getRelativeUrl($media): string
    {
        $path = $media->collection_name . '/' . $media->file_name;
        $prefix = config('media-library.prefix', 'uploads');
        return '/storage/' . $prefix . '/' . $path;
    }

    /**
     * Generate full URL for media
     */
    private function getFullUrl($media): string
    {
        return asset($this->getRelativeUrl($media));
    }

    /**
     * Get image dimensions
     */
    private function getImageDimensions($media): ?array
    {
        try {
            $prefix = config('media-library.prefix', 'uploads');
            $fullPath = storage_path('app/public/' . $prefix . '/' . $media->collection_name . '/' . $media->file_name);

            if (file_exists($fullPath)) {
                $imageInfo = getimagesize($fullPath);
                return [
                    'width' => $imageInfo[0] ?? null,
                    'height' => $imageInfo[1] ?? null,
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Could not get image dimensions: ' . $e->getMessage());
        }
        return null;
    }

    /**
     * Generate thumbnails untuk media menggunakan service custom
     */
    private function generateThumbnailsForMedia($media): void
    {
        try {
            // Cek apakah thumbnail diaktifkan
            // Service akan mengecek konfigurasinya sendiri

            // Cek apakah file adalah gambar
            if (!$media->isImage()) {
                return;
            }

            // Dapatkan path file relatif
            $filePath = $media->getRelativeUrlAttribute();
            
            if (!$filePath) {
                Log::warning('File path tidak ditemukan untuk media ID: ' . $media->id);
                return;
            }

            // Generate semua thumbnail
            $thumbnails = $this->thumbnailService->generateAllThumbnails($filePath);
            
            if (!empty($thumbnails)) {
                Log::info('Thumbnails berhasil dibuat untuk media ID: ' . $media->id, [
                    'file_path' => $filePath,
                    'thumbnails' => $thumbnails
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error generating thumbnails for media ID ' . $media->id . ': ' . $e->getMessage());
        }
    }
}
