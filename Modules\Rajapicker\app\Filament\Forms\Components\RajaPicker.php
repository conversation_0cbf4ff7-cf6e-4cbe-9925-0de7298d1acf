<?php

namespace Modules\Rajapicker\Filament\Forms\Components;

use Filament\Forms\Components\Field;
 use Modules\Rajapicker\Models\Media;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Modules\Rajapicker\Services\RajaPickerThumbnailService;

class RajaPicker extends Field
{
    protected string $view = 'rajapicker::components.forms.raja-picker';

    // Konfigurasi default
    protected array $acceptedFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    protected int $maxFileSize = 10; // MB
    protected string $collection = 'default';
    protected bool $multiple = false;
    protected ?string $directory = null;
    protected bool $enablePicker = true;
    protected bool $enableUploader = true;
    protected ?string $placeholder = null;
    protected int $previewSize = 150; // px
    protected bool $showFileName = true;
    protected bool $showFileSize = true;
    protected int $perPage = 12;
    protected bool $byUser = false;
    protected ?int $byUserId = null;
    protected bool $convertWebp = false;

    // penanda apakah metode convertWebp() dipanggil secara eksplisit
    protected bool $convertWebpExplicit = false;

    protected ?RajaPickerThumbnailService $thumbnailService = null;

    /**
     * Mengambil instance thumbnail service (lazy load)
     */
    protected function getThumbnailService(): RajaPickerThumbnailService
    {
        if ($this->thumbnailService === null) {
            $this->thumbnailService = new RajaPickerThumbnailService();
        }

        return $this->thumbnailService;
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Set default placeholder
        if (is_null($this->placeholder)) {
            $this->placeholder = 'Pilih atau upload gambar...';
        }

        // Atur default convertWebp dari config bila belum di-set secara eksplisit
        if (!$this->convertWebpExplicit) {
            $this->convertWebp = config('rajapicker.defaults.convert_webp', config('rajapicker.storage.webp.default', false));
        }

        // Setup dehydration untuk memastikan data tersimpan dengan benar
        $this->dehydrateStateUsing(function ($state) {
            // Jika state adalah string JSON, decode dulu
            if (is_string($state) && $this->isJson($state)) {
                $state = json_decode($state, true);
            }

            // Konversi ID ke URL jika diperlukan
            $state = $this->convertIdsToUrls($state);

            // Clean URLs for storage (remove /storage/ prefix)
            $state = $this->cleanUrlsForStorage($state);

            // Untuk multiple selection, pastikan return array
            if ($this->multiple) {
                return is_array($state) ? $state : ($state ? [$state] : []);
            }

            // Untuk single selection, pastikan return single value
            return is_array($state) ? (count($state) > 0 ? $state[0] : null) : $state;
        });

        // Setup hydration untuk memastikan data dimuat dengan benar
        $this->afterStateHydrated(function ($component, $state) {
            // Konversi URL ke ID untuk kompatibilitas backward jika diperlukan
            $state = $this->convertUrlsToIds($state);

            // Pastikan state dalam format yang benar
            if ($this->multiple && !is_array($state)) {
                $component->state($state ? [$state] : []);
            } elseif (!$this->multiple && is_array($state)) {
                $component->state(count($state) > 0 ? $state[0] : null);
            }
        });

        // Make field reactive untuk memastikan perubahan state terdeteksi
        $this->live();

        // Add afterStateUpdated callback untuk debugging
        // $this->afterStateUpdated(function ($state, $component) {
        //     // Log state changes untuk debugging
        //     if (config('app.debug')) {
        //         Log::info('RajaPicker state updated', [
        //             'field' => $component->getName(),
        //             'state' => $state,
        //             'multiple' => $this->multiple
        //         ]);
        //     }
        // });
    }

    /**
     * Check if string is valid JSON
     */
    private function isJson($string): bool
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Remove /storage/ prefix from URL for database storage
     */
    private function removeStoragePrefix(string $url): string
    {
        if (str_starts_with($url, '/storage/')) {
            $cleanUrl = substr($url, 8); // Remove '/storage' (8 characters)
            // Remove leading slash for cleaner storage
            if (str_starts_with($cleanUrl, '/')) {
                $cleanUrl = substr($cleanUrl, 1);
            }
            return $cleanUrl;
        }
        return $url;
    }

    /**
     * Add /storage/ prefix to URL for display purposes
     */
    private function addStoragePrefix(string $url): string
    {
        // Don't add prefix if URL already has it or is a full URL
        if (str_starts_with($url, '/storage/') || str_starts_with($url, 'http')) {
            return $url;
        }

        // Add /storage/ prefix - handle both with and without leading slash
        if (str_starts_with($url, '/')) {
            return '/storage' . $url;
        }

        return '/storage/' . $url;
    }

    /**
     * Clean URL for storage (remove /storage/ prefix and leading slash)
     */
    private function cleanUrlForStorage($url): ?string
    {
        if (empty($url) || !is_string($url)) {
            return $url;
        }

        // Remove /storage/ prefix if present
        $cleanUrl = $this->removeStoragePrefix($url);

        // Remove leading slash for cleaner storage
        if (str_starts_with($cleanUrl, '/') && !str_starts_with($cleanUrl, 'http')) {
            $cleanUrl = substr($cleanUrl, 1);
        }

        return $cleanUrl;
    }

    /**
     * Clean URLs for storage (handle both single URL and array of URLs)
     */
    private function cleanUrlsForStorage($state)
    {
        if (empty($state)) {
            return $state;
        }

        if (is_array($state)) {
            return array_map(function($url) {
                return $this->cleanUrlForStorage($url);
            }, $state);
        }

        return $this->cleanUrlForStorage($state);
    }

    /**
     * Set accepted file types
     */
    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }

    /**
     * Set maximum file size in MB
     */
    public function maxFileSize(int $size): static
    {
        $this->maxFileSize = $size;
        return $this;
    }

    /**
     * Set media collection
     */
    public function collection(string $collection): static
    {
        $this->collection = $collection;
        return $this;
    }

    /**
     * Enable multiple file selection
     */
    public function multiple(bool $multiple = true): static
    {
        $this->multiple = $multiple;
        return $this;
    }

    /**
     * Set upload directory
     */
    public function directory(string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }

    /**
     * Enable/disable picker functionality
     */
    public function enablePicker(bool $enable = true): static
    {
        $this->enablePicker = $enable;
        return $this;
    }

    /**
     * Enable/disable uploader functionality
     */
    public function enableUploader(bool $enable = true): static
    {
        $this->enableUploader = $enable;
        return $this;
    }

    /**
     * Set placeholder text
     */
    public function placeholder(string $placeholder): static
    {
        $this->placeholder = $placeholder;
        return $this;
    }

    /**
     * Set preview image size
     */
    public function previewSize(int $size): static
    {
        $this->previewSize = $size;
        return $this;
    }

    /**
     * Show/hide file name
     */
    public function showFileName(bool $show = true): static
    {
        $this->showFileName = $show;
        return $this;
    }

    /**
     * Show/hide file size
     */
    public function showFileSize(bool $show = true): static
    {
        $this->showFileSize = $show;
        return $this;
    }

    /**
     * Filter media by current user
     */
    public function byUser(bool $byUser = true): static
    {
        $this->byUser = $byUser;
        return $this;
    }

    /**
     * Filter media by specific user ID
     * @param int|null $userId User ID to filter by, or null to use current authenticated user. If not provided, defaults to current authenticated user.
     */
    public function byUserId(?int $userId = null): static
    {
        $this->byUserId = $userId ?? Auth::id();
        return $this;
    }

    /**
     * Enable/disable WebP conversion
     */
    public function convertWebp(bool $convert = false): static
    {
        $this->convertWebp = $convert;
        $this->convertWebpExplicit = true;
        return $this;
    }

    // Getter methods untuk view
    public function getAcceptedFileTypes(): array
    {
        return $this->acceptedFileTypes;
    }

    public function getAcceptedFileTypesString(): string
    {
        return implode(',', $this->acceptedFileTypes);
    }

    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }

    public function getMaxFileSizeBytes(): int
    {
        return $this->maxFileSize * 1024 * 1024;
    }

    public function getCollection(): string
    {
        return $this->collection;
    }

    public function isMultiple(): bool
    {
        return $this->multiple;
    }

    public function getDirectory(): ?string
    {
        return $this->directory;
    }

    public function isPickerEnabled(): bool
    {
        return $this->enablePicker;
    }

    public function isUploaderEnabled(): bool
    {
        return $this->enableUploader;
    }

    public function getPlaceholder(): string
    {
        return $this->placeholder;
    }

    public function getPreviewSize(): int
    {
        return $this->previewSize;
    }

    public function shouldShowFileName(): bool
    {
        return $this->showFileName;
    }

    public function shouldShowFileSize(): bool
    {
        return $this->showFileSize;
    }

    public function isByUser(): bool
    {
        return $this->byUser;
    }

    public function getByUserId(): ?int
    {
        return $this->byUserId;
    }

    public function shouldConvertWebp(): bool
    {
        return $this->convertWebp;
    }

    /**
     * Get available media for picker
     */
    public function getAvailableMedia(): Collection
    {
        $query = Media::where('collection_name', $this->collection)
            ->where('mime_type', 'LIKE', 'image/%');

        // Filter by specific user ID if byUserId is set
        if ($this->byUserId !== null) {
            $query->where('user_id', $this->byUserId);
        }
        // Filter by current user if byUser is enabled and byUserId is not set
        elseif ($this->byUser && Auth::check()) {
            $query->where('user_id', Auth::id());
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get all available media from all collections (excluding conversion directory)
     */
    public function getAllAvailableMedia(): Collection
    {
        $query = Media::where('mime_type', 'LIKE', 'image/%')
            ->where(function ($query) {
                // Exclude conversion directory files
                $query->where('collection_name', '!=', 'conversion')
                      ->whereNotLike('file_name', '%/conversion/%');
            });

        // Filter by specific user ID if byUserId is set
        if ($this->byUserId !== null) {
            $query->where('user_id', $this->byUserId);
        }
        // Filter by current user if byUser is enabled and byUserId is not set
        elseif ($this->byUser && Auth::check()) {
            $query->where('user_id', Auth::id());
        }

        return $query->orderBy('created_at', 'desc')
            ->limit(200) // Batasi untuk performa
            ->get();
    }

    /**
     * Get media by ID
     */
    public function getMediaById($id): ?Media
    {
        if (empty($id)) {
            return null;
        }

        return Media::find($id);
    }

    /**
     * Get media by IDs (for multiple)
     */
    public function getMediaByIds(array $ids): Collection
    {
        if (empty($ids)) {
            return collect();
        }

        return Media::whereIn('id', $ids)->get();
    }

    /**
     * Convert IDs to URLs (without /storage/ prefix for storage)
     */
    private function convertIdsToUrls($state)
    {
        if (empty($state)) {
            return $state;
        }

        // Jika sudah berupa URL, clean it for storage
        if (is_string($state) && (str_starts_with($state, 'http') || str_starts_with($state, '/'))) {
            return $this->cleanUrlForStorage($state);
        }

        if (is_array($state)) {
            $urls = [];
            foreach ($state as $item) {
                if (is_numeric($item)) {
                    $media = Media::find($item);
                    if ($media) {
                        // Use relative URL (without /storage/ prefix) for storage
                        $urls[] = $media->relative_url ?? $this->cleanUrlForStorage($media->url);
                    }
                } elseif (is_string($item) && (str_starts_with($item, 'http') || str_starts_with($item, '/'))) {
                    $urls[] = $this->cleanUrlForStorage($item);
                }
            }
            return $urls;
        } elseif (is_numeric($state)) {
            $media = Media::find($state);
            return $media ? ($media->relative_url ?? $this->cleanUrlForStorage($media->url)) : null;
        }

        return $state;
    }

    /**
     * Convert URLs to IDs for backward compatibility
     */
    private function convertUrlsToIds($state)
    {
        if (empty($state)) {
            return $state;
        }

        // Jika sudah berupa ID numeric, return as is
        if (is_numeric($state)) {
            return $state;
        }

        if (is_array($state)) {
            $ids = [];
            foreach ($state as $item) {
                if (is_string($item) && (str_starts_with($item, 'http') || str_starts_with($item, '/'))) {
                    // Normalize URL (add /storage/ prefix if needed for matching)
                    $normalizedUrl = $this->addStoragePrefix($item);

                    // Extract filename dari URL untuk mencari media
                    $filename = basename(parse_url($normalizedUrl, PHP_URL_PATH));
                    $media = Media::where('file_name', $filename)
                        ->where('collection_name', $this->collection)
                        ->first();
                    if ($media) {
                        $ids[] = $media->id;
                    }
                } elseif (is_numeric($item)) {
                    $ids[] = $item;
                }
            }
            return $ids;
        } elseif (is_string($state) && (str_starts_with($state, 'http') || str_starts_with($state, '/'))) {
            // Normalize URL (add /storage/ prefix if needed for matching)
            $normalizedUrl = $this->addStoragePrefix($state);

            // Extract filename dari URL untuk mencari media
            $filename = basename(parse_url($normalizedUrl, PHP_URL_PATH));
            $media = Media::where('file_name', $filename)
                ->where('collection_name', $this->collection)
                ->first();
            return $media ? $media->id : null;
        }

        return $state;
    }

    /**
     * Get thumbnail URL for media
     */
    public function getThumbnailUrl($media, string $sizeName = 'small'): ?string
    {
        if (!$media) {
            return null;
        }

        // Jika media adalah string (URL), gunakan langsung
        if (is_string($media)) {
            return $this->getThumbnailService()->getThumbnailUrl($media, $sizeName);
        }

        // Jika media adalah object, dapatkan URL-nya
        if (is_object($media) && method_exists($media, 'getRelativeUrlAttribute')) {
            $filePath = $media->getRelativeUrlAttribute();
            return $this->getThumbnailService()->getThumbnailUrl($filePath, $sizeName);
        }

        return null;
    }

    /**
     * Get all thumbnail URLs for media
     */
    public function getAllThumbnailUrls($media): array
    {
        if (!$media) {
            return [];
        }

        // Jika media adalah string (URL), gunakan langsung
        if (is_string($media)) {
            return $this->getThumbnailService()->getAllThumbnailUrls($media);
        }

        // Jika media adalah object, dapatkan URL-nya
        if (is_object($media) && method_exists($media, 'getRelativeUrlAttribute')) {
            $filePath = $media->getRelativeUrlAttribute();
            return $this->getThumbnailService()->getAllThumbnailUrls($filePath);
        }

        return [];
    }
}
