<?php

namespace Modules\RajaCms\View\Components;

use Modules\RajaCms\Models\Cms;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\View\Component;

class SlideShow extends Component
{
    public $data;
    public $tinggi;
    public $batas;
    public $durasi;
    public $autoplay;

    /**
     * Create a new component instance.
     */
    public function __construct($tinggi = 700, $batas = 5, $durasi = 5000, $autoplay = true)
    {
        // Validasi dan set parameter tinggi
        $this->tinggi = $this->validatePositiveInteger($tinggi, 700);

        // Validasi dan set parameter batas
        $this->batas = $this->validatePositiveInteger($batas, 5);

        // Validasi dan set parameter durasi (dalam milidetik)
        $this->durasi = $this->validatePositiveInteger($durasi, 5000);

        // Validasi dan set parameter autoplay (boolean)
        $this->autoplay = $this->validateBoolean($autoplay, true);

        $this->data = $this->data();
    }

    public function data()
    {
        $cms = Cms::where('status', 'home')->first();
        $allData = $cms->getJcol('slideshow', []);

        // Terapkan batas jika data lebih dari batas yang ditentukan
        if (count($allData) > $this->batas) {
            return array_slice($allData, 0, $this->batas);
        }

        return $allData;
    }

    /**
     * Validasi input untuk memastikan nilai adalah integer positif
     */
    private function validatePositiveInteger($value, $default)
    {
        // Konversi ke integer
        $intValue = (int) $value;

        // Pastikan nilai adalah integer positif
        if ($intValue > 0) {
            return $intValue;
        }

        // Kembalikan nilai default jika tidak valid
        return $default;
    }

    /**
     * Validasi input untuk memastikan nilai adalah boolean
     */
    private function validateBoolean($value, $default)
    {
        // Jika sudah boolean, return langsung
        if (is_bool($value)) {
            return $value;
        }

        // Konversi string ke boolean
        if (is_string($value)) {
            $value = strtolower(trim($value));
            if (in_array($value, ['true', '1', 'yes', 'on'])) {
                return true;
            }
            if (in_array($value, ['false', '0', 'no', 'off'])) {
                return false;
            }
        }

        // Konversi numeric ke boolean
        if (is_numeric($value)) {
            return (bool) $value;
        }

        // Kembalikan nilai default jika tidak valid
        return $default;
    }

    public function render(): View|Closure|string
    {
        try {
           
            return view('tema::modul.slideshow', [
                'data'     => $this->data,
                'tinggi'   => $this->tinggi,
                'batas'    => $this->batas,
                'durasi'   => $this->durasi,
                'autoplay' => $this->autoplay,
            ]);
        } catch (\Exception $e) {
            // Log::error('SlideShow Component Error: ' . $e->getMessage());
            return view('components.kosong');
        }
    }
} 