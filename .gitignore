/.phpunit.cache
/node_modules
/backup
/bootstrap/cache/*
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/backupdb
/storage/framework
/storage/livewire-tmp
/storage/debugbar
/storage/logs
/storage/app
/storage/cms
/storage/laravel-migrations-seeders
/storage/media
/storage/pembayaran
/storage/produk
/storage/temp-uploads
/storage/toko
/vendor
.env
.env.backup
.env.production
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
"laradumps.yaml" 
/resources/views/testing/*.blade.php
/.tmp.driveupload

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
