<?php

namespace Modules\Rajapicker\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Mo<PERSON>les\Rajapicker\Services\RajaPickerThumbnailService;
use Mo<PERSON>les\Rajapicker\Services\RajaPickerConfigService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class RajaPickerThumbnailController extends Controller
{
    protected RajaPickerThumbnailService $thumbnailService;
    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        $this->thumbnailService = new RajaPickerThumbnailService();
        $this->configService = new RajaPickerConfigService();
    }

    /**
     * Generate thumbnail untuk file tertentu
     */
    public function generate(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
            'size' => 'string|in:small,medium,large',
        ]);

        $filePath = $request->input('file_path');
        $size = $request->input('size', 'small');

        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail generation tidak diaktifkan'
                ], 400);
            }

            // Cek apakah file asli ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File tidak ditemukan'
                ], 404);
            }

            // Generate thumbnail
            $thumbnailPath = $this->thumbnailService->generateThumbnail($filePath, $size);

            if ($thumbnailPath) {
                $thumbnailUrl = $this->configService->generateThumbnailUrl($filePath, $size);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Thumbnail berhasil dibuat',
                    'data' => [
                        'thumbnail_path' => $thumbnailPath,
                        'thumbnail_url' => $thumbnailUrl,
                        'size' => $size,
                        'original_path' => $filePath,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal membuat thumbnail'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error("Error generating thumbnail: " . $e->getMessage(), [
                'file_path' => $filePath,
                'size' => $size
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Generate semua thumbnail untuk file tertentu
     */
    public function generateAll(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->input('file_path');

        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail generation tidak diaktifkan'
                ], 400);
            }

            // Cek apakah file asli ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File tidak ditemukan'
                ], 404);
            }

            // Generate semua thumbnail
            $thumbnailPaths = $this->thumbnailService->generateAllThumbnails($filePath);
            $thumbnailUrls = $this->thumbnailService->getAllThumbnailUrls($filePath);

            return response()->json([
                'success' => true,
                'message' => 'Semua thumbnail berhasil dibuat',
                'data' => [
                    'thumbnail_paths' => $thumbnailPaths,
                    'thumbnail_urls' => $thumbnailUrls,
                    'original_path' => $filePath,
                    'generated_count' => count($thumbnailPaths),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Error generating all thumbnails: " . $e->getMessage(), [
                'file_path' => $filePath
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Dapatkan URL thumbnail
     */
    public function getUrl(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
            'size' => 'string|in:small,medium,large',
        ]);

        $filePath = $request->input('file_path');
        $size = $request->input('size', 'small');

        try {
            $thumbnailUrl = $this->thumbnailService->getThumbnailUrl($filePath, $size);

            if ($thumbnailUrl) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'thumbnail_url' => $thumbnailUrl,
                        'size' => $size,
                        'original_path' => $filePath,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail tidak ditemukan dan gagal dibuat'
                ], 404);
            }

        } catch (\Exception $e) {
            Log::error("Error getting thumbnail URL: " . $e->getMessage(), [
                'file_path' => $filePath,
                'size' => $size
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendapatkan thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Dapatkan semua URL thumbnail
     */
    public function getAllUrls(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->input('file_path');

        try {
            $thumbnailUrls = $this->thumbnailService->getAllThumbnailUrls($filePath);

            return response()->json([
                'success' => true,
                'data' => [
                    'thumbnail_urls' => $thumbnailUrls,
                    'original_path' => $filePath,
                    'available_sizes' => array_keys($thumbnailUrls),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Error getting all thumbnail URLs: " . $e->getMessage(), [
                'file_path' => $filePath
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendapatkan thumbnail URLs',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Regenerate thumbnail
     */
    public function regenerate(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
            'size' => 'string|in:small,medium,large',
        ]);

        $filePath = $request->input('file_path');
        $size = $request->input('size', 'small');

        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail generation tidak diaktifkan'
                ], 400);
            }

            // Cek apakah file asli ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File tidak ditemukan'
                ], 404);
            }

            // Regenerate thumbnail
            $thumbnailPath = $this->thumbnailService->regenerateThumbnail($filePath, $size);

            if ($thumbnailPath) {
                $thumbnailUrl = $this->configService->generateThumbnailUrl($filePath, $size);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Thumbnail berhasil di-regenerate',
                    'data' => [
                        'thumbnail_path' => $thumbnailPath,
                        'thumbnail_url' => $thumbnailUrl,
                        'size' => $size,
                        'original_path' => $filePath,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal regenerate thumbnail'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error("Error regenerating thumbnail: " . $e->getMessage(), [
                'file_path' => $filePath,
                'size' => $size
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat regenerate thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Regenerate semua thumbnail
     */
    public function regenerateAll(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->input('file_path');

        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail generation tidak diaktifkan'
                ], 400);
            }

            // Cek apakah file asli ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File tidak ditemukan'
                ], 404);
            }

            // Regenerate semua thumbnail
            $thumbnailPaths = $this->thumbnailService->regenerateAllThumbnails($filePath);
            $thumbnailUrls = $this->thumbnailService->getAllThumbnailUrls($filePath);

            return response()->json([
                'success' => true,
                'message' => 'Semua thumbnail berhasil di-regenerate',
                'data' => [
                    'thumbnail_paths' => $thumbnailPaths,
                    'thumbnail_urls' => $thumbnailUrls,
                    'original_path' => $filePath,
                    'regenerated_count' => count($thumbnailPaths),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Error regenerating all thumbnails: " . $e->getMessage(), [
                'file_path' => $filePath
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat regenerate thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Hapus thumbnail
     */
    public function delete(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->input('file_path');

        try {
            $success = $this->thumbnailService->deleteThumbnails($filePath);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thumbnail berhasil dihapus',
                    'data' => [
                        'original_path' => $filePath,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus thumbnail'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error("Error deleting thumbnails: " . $e->getMessage(), [
                'file_path' => $filePath
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Batch generate thumbnail untuk multiple files
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        $request->validate([
            'file_paths' => 'required|array',
            'file_paths.*' => 'string',
            'size' => 'string|in:small,medium,large',
        ]);

        $filePaths = $request->input('file_paths');
        $size = $request->input('size', 'small');

        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thumbnail generation tidak diaktifkan'
                ], 400);
            }

            // Batch generate thumbnail
            $results = $this->thumbnailService->batchGenerateThumbnails($filePaths, $size);
            
            $successCount = count(array_filter($results));
            $totalCount = count($filePaths);

            return response()->json([
                'success' => true,
                'message' => "Berhasil generate {$successCount} dari {$totalCount} thumbnail",
                'data' => [
                    'results' => $results,
                    'success_count' => $successCount,
                    'total_count' => $totalCount,
                    'size' => $size,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Error batch generating thumbnails: " . $e->getMessage(), [
                'file_paths' => $filePaths,
                'size' => $size
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat batch generate thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Clean up thumbnail yang tidak terpakai
     */
    public function cleanup(Request $request): JsonResponse
    {
        try {
            $deletedCount = $this->thumbnailService->cleanupOrphanedThumbnails();

            return response()->json([
                'success' => true,
                'message' => "Berhasil menghapus {$deletedCount} thumbnail yang tidak terpakai",
                'data' => [
                    'deleted_count' => $deletedCount,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Error cleaning up orphaned thumbnails: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat cleanup thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Dapatkan konfigurasi thumbnail
     */
    public function config(): JsonResponse
    {
        try {
            $config = [
                'enabled' => $this->configService->isThumbnailEnabled(),
                'directory' => $this->configService->getThumbnailDirectory(),
                'prefix' => $this->configService->getThumbnailPrefix(),
                'webp_conversion' => $this->configService->isThumbnailWebpEnabled(),
                'sizes' => $this->configService->getThumbnailSizes(),
                'size_names' => $this->configService->getThumbnailSizeNames(),
            ];

            return response()->json([
                'success' => true,
                'data' => $config
            ]);

        } catch (\Exception $e) {
            Log::error("Error getting thumbnail config: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendapatkan konfigurasi thumbnail',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
} 